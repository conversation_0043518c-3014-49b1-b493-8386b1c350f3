<?php

if (!defined('ABSPATH')) exit; // Exit if accessed directly

class My_Status_Widget extends \Elementor\Widget_Base {

	public function get_name() {
		return 'my_status_widget';
	}

	public function get_title() {
		return __('My Status', 'solidcam');
	}

	public function get_icon() {
		return 'eicon-star-o';
	}

	public function get_categories() {
		return ['general'];
	}

	protected function render() {
		$current_user = wp_get_current_user();

		// Ensure the user is logged in
		if (!$current_user->exists()) {
			echo '<p>' . esc_html__('Please log in to view your status.', 'solidcam') . '</p>';
			return;
		}

		$user_id = $current_user->ID;

		// Fetch customer account details from user meta
		$customer_account = get_user_meta($user_id, 'customer_account', true);

		// Validate the customer account meta
		if (empty($customer_account) || !is_array($customer_account)) {
			echo '<p>' . esc_html__('No status information available.', 'solidcam') . '</p>';
			return;
		}

		// Extract years_on_subs from customer account
		$years_on_subs = isset($customer_account['years_on_subs']) ? intval($customer_account['years_on_subs']) : 0;

		// Define ranks and statuses
		$ranks = [
			2 => __('Aluminium', 'solidcam'),
			4 => __('Brass', 'solidcam'),
			6 => __('Steel', 'solidcam'),
			8 => __('Stainless Steel', 'solidcam'),
			10 => __('Titanium', 'solidcam')
		];

		// Determine the current rank based on years_on_subs
		$current_rank = __('None', 'solidcam');
		foreach ($ranks as $years => $status) {
			if ($years_on_subs >= $years) {
				$current_rank = $status;
			}
		}

		// Render the widget
		echo '<div class="widget-my-status add-on">';
		echo '<h5>' . esc_html__('My Status', 'solidcam') . '</h5>';
		echo '<div class="status-trophies-box">';
		
    		// Render trophy icons
    		echo '<span  data-title="' . esc_attr($years_on_subs . ' years continuous subscription') . '">';
    		foreach ($ranks as $years => $status) {
    			$trophy_class = ($years_on_subs >= $years) ? 'text-warning' : 'text-black-50';
    			echo '<i class="fa-regular fa-trophy ' . esc_attr($trophy_class) . '" aria-hidden="true"></i>';
    		}
    		echo '</span>&emsp;';
    		
    		// Display current rank
    		echo esc_html($current_rank);
    		
    		// Collapsible icon
    		echo '<a class="info-icon" data-title="' . esc_attr__('Show more information', 'solidcam') . '">';
    		    echo '<i class="fal fa-question-circle" aria-hidden="true"></i>';
    		echo '</a>';
		echo '</div>';

		// Collapsible section for details
		echo '<div class="status-table-view">';
    		echo '<div class="card card-body">';
    		echo '<p>' . esc_html__('As a small reward for your loyalty, you will receive a special customer status when you renew your subscription for a few years.', 'solidcam') . '</p>';
    		
    		// Render rank details table
    		echo '<table class="table table-hover table-borderless table-striped">';
    		echo '<thead>';
    		echo '<tr>';
    		echo '<th scope="col">' . esc_html__('Years in a row', 'solidcam') . '</th>';
    		echo '<th scope="col">' . esc_html__('Status', 'solidcam') . '</th>';
    		echo '</tr>';
    		echo '</thead>';
    		echo '<tbody>';
    		foreach ($ranks as $years => $status) {
    			echo '<tr>';
    			echo '<th scope="row">' . esc_html($years) . '</th>';
    			echo '<td>' . esc_html($status) . '</td>';
    			echo '</tr>';
    		}
    		echo '</tbody>';
    		echo '</table>';
    		echo '</div>';
		echo '</div>';

		echo '</div>'; // End of widget
	}
}