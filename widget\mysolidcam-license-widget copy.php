<?php
if (!defined('ABSPATH')) exit;

class Custom_License_Widget extends \Elementor\Widget_Base {
	public function get_name() {
		return 'license_management';
	}

	public function get_title() {
		return __('License Management', 'solidcam');
	}

	public function get_icon() {
		return 'eicon-lock';
	}

	public function get_categories() {
		return ['general'];
	}

	protected function validate_user_access() {
		$user_id = get_current_user_id();
		if (!$user_id) {
			echo '<p>' . esc_html__('You must be logged in to manage licenses.', 'solidcam') . '</p>';
			return false;
		}
		return $user_id;
	}

	protected function get_user_license_data($user_id) {
		return [
			'active_license' => get_user_meta($user_id, 'active_user_license', true),
			'licenses_enabled' => get_user_meta($user_id, 'user_licenses_enabled', true) ?: [],
			'dongle_no' => get_user_meta($user_id, 'dongle_no', true),
			'subscription_status' => get_user_meta($user_id, 'subscription_status', true)
		];
	}

	protected function render_no_licenses_box() {
		?>
		<div class="no-licenses-box">
			<div class="icon-box">
				<i class="fa-solid fa-triangle-exclamation fa-2xl" aria-hidden="true"></i>
			</div>
			<div class="content-box">
				<h4 class="heading"><?php esc_html_e('No dongle number/software key stored', 'solidcam'); ?></h4>
				<p class="description">
					<?php esc_html_e('You can add/update a correct dongle number/software-key to manage your licenses (this is not required)', 'solidcam'); ?>
				</p>
				<a class="add-dongle-button edit-profile-btn" href="#">
					<?php esc_html_e('Add valid dongle number/software-key', 'solidcam'); ?>
					<i class="fa-regular fa-unlock-keyhole ms-2" aria-hidden="true"></i>
				</a>
			</div>
		</div>
		<?php
	}

	protected function render_license_dropdown($licenses_enabled, $active_license) {
		foreach ($licenses_enabled as $license): 
			$license_number = $license['license_number'];
			$sub_end_date = $license['sub_end_date'];
			
			$container_class = 'default-section-license';
			$button_wrapper = '<span class="elementor-button-icon">
			  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
				<path d="M480 96C444.7 96 416 124.7 416 160L416 224L448 224C483.3 224 512 252.7 512 288L512 512C512 547.3 483.3 576 448 576L192 576C156.7 576 128 547.3 128 512L128 288C128 252.7 156.7 224 192 224L352 224L352 160C352 89.3 409.3 32 480 32C550.7 32 608 89.3 608 160L608 192C608 209.7 593.7 224 576 224C558.3 224 544 209.7 544 192L544 160C544 124.7 515.3 96 480 96zM360 424C373.3 424 384 413.3 384 400C384 386.7 373.3 376 360 376L280 376C266.7 376 256 386.7 256 400C256 413.3 266.7 424 280 424L360 424z"></path>
			  </svg>
			</span>
			<span class="elementor-button-text">'. __('Unlock', 'solidcam') .'</span>';
			if( $active_license == $license_number ){
				$container_class = 'active-section-license';
				$button_wrapper = '<span class="elementor-button-icon">
				<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640"><path d="M557 152.9L538.2 178.8L282.2 530.8L260.2 561.1C259.5 560.4 208 508.9 105.7 406.6L83 384L128.3 338.7C130.2 340.6 171.6 382 252.4 462.8L486.4 141.1L505.2 115.2L557 152.8z"></path></svg>			</span>
									<span class="elementor-button-text">'. __('Selected', 'solidcam') .'</span>';
			}
			?>
	
		<div data-dce-background-hover-color="#F5F5F5" class="mysolidcam-section-license <?php echo $container_class; ?>elementor-element elementor-element-65908dc e-con-full e-flex e-con e-child" data-id="65908dc" data-element_type="container">
		  <div class="elementor-element elementor-element-630c953 elementor-view-default elementor-widget elementor-widget-icon" data-id="630c953" data-element_type="widget" data-widget_type="icon.default">
			<div class="elementor-widget-container">
			  <div class="elementor-icon-wrapper">
				<div class="elementor-icon">
				  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
					<path d="M320 96C355.3 96 384 124.7 384 160L384 224L256 224L256 160C256 124.7 284.7 96 320 96zM192 160L192 224C156.7 224 128 252.7 128 288L128 512C128 547.3 156.7 576 192 576L448 576C483.3 576 512 547.3 512 512L512 288C512 252.7 483.3 224 448 224L448 160C448 89.3 390.7 32 320 32C249.3 32 192 89.3 192 160zM344 360L344 440C344 453.3 333.3 464 320 464C306.7 464 296 453.3 296 440L296 360C296 346.7 306.7 336 320 336C333.3 336 344 346.7 344 360z"></path>
				  </svg>
				</div>
			  </div>
			</div>
		  </div>
		  <div class="elementor-element elementor-element-9b73037 elementor-widget elementor-widget-heading" data-id="9b73037" data-element_type="widget" data-widget_type="heading.default">
			<div class="elementor-widget-container">
			  <div class="elementor-heading-title elementor-size-default"><?php echo $license_number; ?></div>
			</div>
		  </div>
		  <div class="elementor-element elementor-element-2492388 mysc-tooltip elementor-view-default elementor-widget elementor-widget-icon" data-id="2492388" data-element_type="widget" data-tooltip="Valid until: <?php echo $sub_end_date; ?>" data-widget_type="icon.default">
			<div class="elementor-widget-container">
			  <div class="elementor-icon-wrapper">
				<div class="elementor-icon">
				  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
					<path d="M320 576C461.4 576 576 461.4 576 320C576 178.6 461.4 64 320 64C178.6 64 64 178.6 64 320C64 461.4 178.6 576 320 576zM280 400L304 400L304 336L256 336L256 288L352 288L352 400L384 400L384 448L256 448L256 400L280 400zM352 256L288 256L288 192L352 192L352 256z"></path>
				  </svg>
				</div>
			  </div>
			</div>
		  </div>
		  <div class="elementor-element elementor-element-83be3ab elementor-align-right elementor-widget elementor-widget-button" data-id="83be3ab" data-element_type="widget" data-widget_type="button.default">
			<div class="elementor-widget-container">
			  <div class="elementor-button-wrapper">
				<a class="elementor-button elementor-button-link elementor-size-sm" href="#">
				  <span class="elementor-button-content-wrapper">
					<?php echo $button_wrapper; ?>
				  </span>
				</a>
			  </div>
			</div>
		  </div>
		</div>
		<?php endforeach; ?>
		<!-- <select id="license-dropdown">
			<option value=""><?php esc_html_e('Select License', 'solidcam'); ?></option>
			<?php foreach ($licenses_enabled as $license): ?>
				<option value="<?php echo esc_attr($license); ?>" 
						<?php selected($license, $active_license); ?>>
					<?php echo esc_html($license); ?>
				</option>
			<?php endforeach; ?>
		</select> -->
		<?php
	}

	protected function render_action_buttons() {
		?>
		<button id="remove-license-btn" class="button" data-title="<?php esc_attr_e('Remove the selected Dongle/Software key', 'solidcam'); ?>">
			<i class="fa-light fa-arrows-rotate loader-icon" aria-hidden="true" style="display: none;"></i>
			<i class="fal fa-minus" aria-hidden="true"></i> 
			<span><?php esc_html_e('Remove', 'solidcam'); ?></span>
		</button>
		<button id="toggle-add-license-btn" class="button" data-title="<?php esc_attr_e('Add new Dongle/Software-Key', 'solidcam'); ?>">
			<i class="fa-light fa-arrows-rotate loader-icon" aria-hidden="true" style="display: none;"></i>
			<i class="fal fa-plus" aria-hidden="true"></i> 
			<span><?php esc_html_e('Add', 'solidcam'); ?></span>
		</button>
		<?php
	}

	protected function render_add_license_form() {
		?>
		<div id="add-license-form" class="toggle-form" style="display:none;">
			<label for="add-license"><?php esc_html_e('New Dongle/Software-Key:', 'solidcam'); ?></label>
			<input type="text" id="add-license" style="margin-bottom: 16px;" 
				   pattern="[A-Za-z0-9-]+" title="<?php esc_attr_e('Only alphanumeric characters and hyphens allowed', 'solidcam'); ?>" />
			<div class="checkbox-box">
				<input type="checkbox" id="authorization-checkbox" required />
				<label for="authorization-checkbox">
					<?php esc_html_e('I am authorized to register this SolidCAM Dongle/Software-Key for my company!', 'solidcam'); ?>
				</label>
			</div>
			<button id="send-license-btn" class="button button-primary">
				<?php esc_html_e('Send New Dongle for Registration', 'solidcam'); ?>
			</button>
		</div>
		<?php
	}
	

	protected function render_remove_license_confirm() {
		?>
		<div id="remove-license-confirm" class="toggle-form" style="display:none;">
			<p>
				<?php esc_html_e('Please confirm to delete the key:', 'solidcam'); ?> 
				<span id="selected-license-key"></span>
			</p>
			<button id="confirm-remove-license-btn" class="button button-danger">
				<?php esc_html_e('Remove Dongle/Software-Key', 'solidcam'); ?>
			</button>
		</div>
		<?php
	}

	protected function render() {
		$user_id = $this->validate_user_access();
		if (!$user_id) return;

		$license_data = $this->get_user_license_data($user_id);
		$active_license = $license_data['active_license'];
		$licenses_enabled = $license_data['licenses_enabled'];
		$total_licenses = count($licenses_enabled);

		// Validate active license
		if (!in_array($active_license, $licenses_enabled)) {
			$active_license = null;
		}

		?>
		<div id="license-management-widget" class="custom-license-widget">
			<h5><?php printf(esc_html__('My License (%d)', 'solidcam'), $total_licenses); ?></h5>

			<?php
			if (empty($license_data['dongle_no']) || $license_data['subscription_status'] == 'false') {
				$this->render_no_licenses_box();
			}

			if ($total_licenses > 1) {
				echo '<h2>' . ($active_license ? esc_html($active_license) : esc_html__('No active license', 'solidcam')) . '</h2>';
			}
			?>

			<div class="btn-action-toolbar">
				<?php 
				$customer_all_licenses = get_user_meta($user_id,'customer_all_licenses', true);
				$this->render_license_dropdown($customer_all_licenses, $active_license);
				$this->render_action_buttons();
				?>
			</div>

			<?php
			$this->render_add_license_form();
			$this->render_remove_license_confirm();
			?>
		</div>
	
		<?php
	}
}