<?php
/**
 * SolidCAM Website User Management - Complete Version
 * Handles user registration, authentication, password recovery, and multi-role support
 * Includes admin table columns and caching for performance
 */

const USER_ROLES = [
	'CUSTOMER' => 'customer',
	'RESELLER' => 'reseller',
	'STAFF' => 'staff',
	'PARTNER' => 'partner',
	'MAKER' => 'maker'
];

const LEGACY_USER_GROUPS = [
	1 => USER_ROLES['CUSTOMER'],    // Basic customer
	2 => USER_ROLES['CUSTOMER'],    // Customer
	3 => USER_ROLES['CUSTOMER'],    // Customer variant
	4 => USER_ROLES['STAFF'],       // Staff
	5 => USER_ROLES['RESELLER'],    // Reseller
	16 => USER_ROLES['PARTNER'],    // Partner
	61 => USER_ROLES['CUSTOMER'],   // Customer group
	63 => USER_ROLES['CUSTOMER'],   // Customer group
	64 => USER_ROLES['CUSTOMER'],   // Customer group
	65 => USER_ROLES['CUSTOMER']    // Customer group
];

const FORM_IDS = [
	'CUSTOMER_REGISTRATION' => 'customer_registration',
	'RESELLER_PARTNER' => 'reseller_partner',
	'USER_UPDATE' => 'user_update_form',
	'MAKER_REGISTRATION' => 'maker_registration',
	
];

const META_FIELDS = ['company', 'address', 'zip', 'city', 'telephone', 'country', 'state', 'preferred_language', 'dongle_no'];

// Cache expiration time (in seconds) - 1 hour
const CACHE_EXPIRATION = 3600;

/**
 * Multi-Role Support Functions
 */

/**
 * Parse multiple user groups from legacy TYPO3 format
 * @param string $usergroup_string Comma-separated usergroup IDs (e.g., "4,5,63")
 * @return array Array of WordPress role names
 */
function parse_legacy_user_groups($usergroup_string) {
	if (empty($usergroup_string)) {
		return [USER_ROLES['CUSTOMER']];
	}
	
	$group_ids = array_map('trim', explode(',', $usergroup_string));
	$roles = [];
	
	foreach ($group_ids as $group_id) {
		if (isset(LEGACY_USER_GROUPS[$group_id])) {
			$roles[] = LEGACY_USER_GROUPS[$group_id];
		}
	}
	
	if (empty($roles)) {
		$roles[] = USER_ROLES['CUSTOMER'];
	}
	
	return array_unique($roles);
}

/**
 * Get primary role (highest priority role for WordPress)
 * Priority: staff > reseller > partner > customer
 */
function get_primary_role($roles) {
	$priority_order = ['staff', 'reseller', 'partner', 'customer', 'maker'];
	
	foreach ($priority_order as $priority_role) {
		if (in_array($priority_role, $roles)) {
			return $priority_role;
		}
	}
	
	return USER_ROLES['CUSTOMER'];
}

/**
 * Assign multiple roles to WordPress user with caching
 */
function assign_multiple_roles_to_user($user_id, $roles) {
	$user = new WP_User($user_id);
	
	$primary_role = get_primary_role($roles);
	$user->set_role($primary_role);
	
	// Store all roles and cache data
	update_user_meta($user_id, 'all_user_roles', $roles);
	update_user_meta($user_id, 'roles_last_updated', time());
	
	// Clear cache when roles are updated
	delete_transient('legacy_user_data_' . $user_id);
}

/**
 * Caching Functions
 */

/**
 * Get legacy user data with caching
 */
function get_cached_legacy_user_data($email) {
	$cache_key = 'legacy_user_' . md5($email);
	$cached_data = get_transient($cache_key);
	
	if ($cached_data !== false) {
		return $cached_data;
	}
	
	global $wpdb;
	$legacy_user = $wpdb->get_row($wpdb->prepare(
		"SELECT uid, email, usergroup, first_name, last_name, company, tx_feuserregisterextend_dongle as dongle_no FROM fe_users WHERE email = %s",
		$email
	));
	
	if ($legacy_user) {
		$legacy_user->parsed_roles = parse_legacy_user_groups($legacy_user->usergroup);
		$legacy_user->primary_role = get_primary_role($legacy_user->parsed_roles);
	}
	
	// Cache for 1 hour
	set_transient($cache_key, $legacy_user, CACHE_EXPIRATION);
	
	return $legacy_user;
}

/**
 * Get user comparison data with caching
 */
function get_user_comparison_data($user_id) {
	$cache_key = 'user_comparison_' . $user_id;
	$cached_data = get_transient($cache_key);
	
	if ($cached_data !== false) {
		return $cached_data;
	}
	
	$user = get_userdata($user_id);
	if (!$user) {
		return null;
	}
	
	$legacy_data = get_cached_legacy_user_data($user->user_email);
	$wp_roles = get_user_all_roles($user_id);
	
	$comparison_data = [
		'wp_user_id' => $user_id,
		'email' => $user->user_email,
		'wp_primary_role' => $user->roles[0] ?? 'none',
		'wp_all_roles' => $wp_roles,
		'legacy_exists' => !empty($legacy_data),
		'legacy_usergroup' => $legacy_data->usergroup ?? 'N/A',
		'legacy_roles' => $legacy_data->parsed_roles ?? [],
		'legacy_primary_role' => $legacy_data->primary_role ?? 'none',
		'roles_match' => false,
		'dongle_no' => get_user_meta($user_id, 'dongle_no', true)
	];
	
	// Check if roles match
	if ($legacy_data) {
		if (!empty($wp_roles)) {
			// User has multi-role data stored - compare arrays
			$comparison_data['roles_match'] = (
				$comparison_data['wp_primary_role'] === $comparison_data['legacy_primary_role'] &&
				count(array_diff($wp_roles, $legacy_data->parsed_roles)) === 0 &&
				count(array_diff($legacy_data->parsed_roles, $wp_roles)) === 0
			);
		} else {
			// User doesn't have multi-role data - compare primary roles only
			$comparison_data['roles_match'] = ($comparison_data['wp_primary_role'] === $comparison_data['legacy_primary_role']);
		}
	}
	
	// Cache for 1 hour
	set_transient($cache_key, $comparison_data, CACHE_EXPIRATION);
	
	return $comparison_data;
}

/**
 * WordPress Admin Table Columns
 */

// Add custom columns to users table
add_filter('manage_users_columns', 'add_solidcam_user_columns');
add_filter('manage_users_custom_column', 'show_solidcam_user_column_content', 10, 3);
add_filter('manage_users_sortable_columns', 'make_solidcam_columns_sortable');

function add_solidcam_user_columns($columns) {
	$columns['legacy_status'] = 'Legacy User';
	$columns['all_roles'] = 'All Roles';
	$columns['typo3_roles'] = 'TYPO3 Roles';
	$columns['role_sync'] = 'Role Sync';
	$columns['dongle_info'] = 'Dongle';
	return $columns;
}

function show_solidcam_user_column_content($value, $column_name, $user_id) {
	switch ($column_name) {
		case 'legacy_status':
			$comparison = get_user_comparison_data($user_id);
			if ($comparison && $comparison['legacy_exists']) {
				return '<span style="color: green; font-weight: bold;">✓ Found</span>';
			} else {
				return '<span style="color: orange; font-weight: bold;">⚠ Not Found</span>';
			}
			
		case 'all_roles':
			$wp_roles = get_user_all_roles($user_id);
			if (!empty($wp_roles)) {
				return '<div style="font-size: 12px;"><strong>' . implode('</strong><br><strong>', $wp_roles) . '</strong></div>';
			} else {
				$user = get_userdata($user_id);
				$primary_role = !empty($user->roles) ? $user->roles[0] : 'none';
				return '<span style="color: #666;"><strong>' . esc_html($primary_role) . '</strong></span>';
			}
			
		case 'typo3_roles':
			$comparison = get_user_comparison_data($user_id);
			if ($comparison && $comparison['legacy_exists']) {
				$output = '<div style="font-size: 11px;">';
				$output .= '<code style="background: #f0f0f1; padding: 2px 4px; border-radius: 3px;">' . esc_html($comparison['legacy_usergroup']) . '</code>';
				if (!empty($comparison['legacy_roles'])) {
					$output .= '<br><small style="color: #135e96;">' . esc_html(implode(', ', $comparison['legacy_roles'])) . '</small>';
				}
				$output .= '</div>';
				return $output;
			} else {
				return '<span style="color: #ccc; font-style: italic;">N/A</span>';
			}
			
		case 'role_sync':
			$comparison = get_user_comparison_data($user_id);
			if (!$comparison || !$comparison['legacy_exists']) {
				return '<span style="color: #ccc; font-style: italic;">N/A</span>';
			} elseif ($comparison['roles_match']) {
				return '<span style="color: green; font-weight: bold;">✓ Synced</span>';
			} else {
				$output = '<div style="text-align: center;">';
				$output .= '<span style="color: red; font-weight: bold;">✗ Mismatch</span><br>';
				$output .= '<button type="button" onclick="syncUserRoles(' . $user_id . ')" class="button button-small" style="margin-top: 3px; font-size: 10px;">Sync</button>';
				$output .= '</div>';
				return $output;
			}
			
		case 'dongle_info':
			$dongle = get_user_meta($user_id, 'dongle_no', true);
			if ($dongle) {
				$display_dongle = strlen($dongle) > 12 ? substr($dongle, 0, 8) . '...' : $dongle;
				return '<code title="' . esc_attr($dongle) . '" style="background: #f0f0f1; padding: 2px 4px; border-radius: 3px; font-size: 11px;">' . esc_html($display_dongle) . '</code>';
			} else {
				return '<span style="color: #ccc; font-style: italic;">None</span>';
			}
	}
	
	return $value;
}

function make_solidcam_columns_sortable($columns) {
	$columns['legacy_status'] = 'legacy_status';
	$columns['role_sync'] = 'role_sync';
	return $columns;
}

// Add CSS and JavaScript for admin
add_action('admin_head-users.php', 'solidcam_admin_styles');

function solidcam_admin_styles() {
	?>
	<style>
	/* SolidCAM User Table Columns */
	.column-legacy_status, .column-role_sync { 
		width: 90px; 
		text-align: center;
	}
	.column-typo3_roles { 
		width: 140px; 
		text-align: center;
	}
	.column-dongle_info { 
		width: 110px; 
		text-align: center;
	}
	.column-all_roles { 
		width: 160px; 
	}
	
	/* Improve readability */
	.users .column-legacy_status,
	.users .column-all_roles,
	.users .column-typo3_roles,
	.users .column-role_sync,
	.users .column-dongle_info {
		vertical-align: top;
		padding: 8px 5px;
		line-height: 1.4;
	}
	
	/* Make small buttons more compact */
	.button-small {
		padding: 2px 6px !important;
		font-size: 10px !important;
		line-height: 1.2 !important;
		height: auto !important;
	}
	
	/* Better spacing for role lists */
	.users .column-all_roles strong {
		display: inline-block;
		margin-right: 4px;
		margin-bottom: 2px;
		padding: 1px 4px;
		background: #e8f4fd;
		border-radius: 3px;
		font-size: 11px;
	}
	
	/* Status indicators */
	.solidcam-status-found { color: #00a32a !important; }
	.solidcam-status-missing { color: #d63638 !important; }
	.solidcam-status-synced { color: #00a32a !important; }
	.solidcam-status-mismatch { color: #d63638 !important; }
	</style>
	<script>
	function syncUserRoles(userId) {
		if (confirm('Sync roles for this user with TYPO3 data?')) {
			// Show loading state
			var button = event.target;
			var originalText = button.innerHTML;
			button.innerHTML = 'Syncing...';
			button.disabled = true;
			
			// AJAX call to sync roles
			jQuery.post(ajaxurl, {
				action: 'sync_user_roles',
				user_id: userId,
				nonce: '<?php echo wp_create_nonce("sync_user_roles"); ?>'
			}, function(response) {
				if (response.success) {
					// Reload the page to show updated data
					location.reload();
				} else {
					alert('Error: ' + (response.data || 'Unknown error occurred'));
					button.innerHTML = originalText;
					button.disabled = false;
				}
			}).fail(function() {
				alert('Network error occurred. Please try again.');
				button.innerHTML = originalText;
				button.disabled = false;
			});
		}
	}
	
	// Add search functionality for SolidCAM columns
	jQuery(document).ready(function($) {
		// Add filter buttons
		if ($('.tablenav.top .alignleft.actions').length) {
			var filterHtml = '<select id="solidcam-role-filter" style="margin-left: 10px;">' +
				'<option value="">All Role Sync Status</option>' +
				'<option value="synced">Synced</option>' +
				'<option value="mismatch">Mismatch</option>' +
				'<option value="no-legacy">No Legacy Data</option>' +
				'</select>';
			$('.tablenav.top .alignleft.actions').first().append(filterHtml);
			
			$('#solidcam-role-filter').on('change', function() {
				var filter = $(this).val();
				var rows = $('.wp-list-table tbody tr');
				
				if (!filter) {
					rows.show();
					return;
				}
				
				rows.each(function() {
					var $row = $(this);
					var syncCell = $row.find('.column-role_sync');
					var shouldShow = false;
					
					if (filter === 'synced' && syncCell.text().includes('Synced')) {
						shouldShow = true;
					} else if (filter === 'mismatch' && syncCell.text().includes('Mismatch')) {
						shouldShow = true;
					} else if (filter === 'no-legacy' && syncCell.text().includes('N/A')) {
						shouldShow = true;
					}
					
					$row.toggle(shouldShow);
				});
			});
		}
	});
	</script>
	<?php
}

// AJAX handler for role sync
add_action('wp_ajax_sync_user_roles', 'ajax_sync_user_roles');

function ajax_sync_user_roles() {
	check_ajax_referer('sync_user_roles', 'nonce');
	
	if (!current_user_can('edit_users')) {
		wp_die('Insufficient permissions');
	}
	
	$user_id = intval($_POST['user_id']);
	$user = get_userdata($user_id);
	
	if (!$user) {
		wp_send_json_error('User not found');
	}
	
	$legacy_data = get_cached_legacy_user_data($user->user_email);
	
	if (!$legacy_data) {
		wp_send_json_error('Legacy user data not found');
	}
	
	// Sync roles
	assign_multiple_roles_to_user($user_id, $legacy_data->parsed_roles);
	
	// Clear cache
	delete_transient('user_comparison_' . $user_id);
	
	wp_send_json_success('Roles synced successfully');
}

/**
 * Original Functions with Multi-Role Support
 */

// Handle Elementor form submissions
add_action('elementor_pro/forms/new_record', 'handle_registration_submission', 10, 2);

function get_form_data($record) {
	$raw_fields = $record->get('fields');
	$form_data = [];
	foreach ($raw_fields as $field) {
		$form_data[$field['id']] = $field['value'];
	}
	return $form_data;
}

function validate_registration($form_data, $handler) {
	$errors = [];
	
	if ($form_data['password'] !== $form_data['repeat_password']) {
		$errors['password'] = __('Passwords do not match. Please try again.', 'solidcam');
	}
	
	if (email_exists($form_data['email'])) {
		$errors['email'] = __('Email already registered. Please use different email or reset password.', 'solidcam');
	}
	
	global $wpdb;
	if ($wpdb->get_row($wpdb->prepare("SELECT * FROM fe_users WHERE email = %s", $form_data['email']))) {
		$errors['email'] = __('Email already registered. Please use different email or reset password.', 'solidcam');
	}
	
	if( isset($form_data['dongle_no']) ){
		$verify_dongle = verify_dongle_with_solidcam($form_data['dongle_no']);
		if (empty($verify_dongle) || isset($verify_dongle['error'])) {
			$errors['dongle_no'] = __('Invalid Dongle-No., Product-Key, Serial No., or Activation-ID.', 'solidcam');
		}
	}
	
	
	foreach ($errors as $field => $message) {
		$handler->add_error($field, $message);
	}
	
	return empty($errors);
}

function get_user_role($account_type) {
	return $account_type ? $account_type ?? $account_type : USER_ROLES['CUSTOMER'];
}

function create_wp_user($form_data, $user_role) {
	$userdata = [
		'user_login' => $form_data['email'],
		'user_email' => $form_data['email'],
		'user_pass' => $form_data['password'],
		'first_name' => $form_data['first_name'],
		'last_name' => $form_data['last_name'],
		'display_name' => "{$form_data['first_name']} {$form_data['last_name']}",
		'role' => $user_role
	];
	
	$user_id = wp_insert_user($userdata);
	if (!is_wp_error($user_id)) {
		foreach (META_FIELDS as $field) {
			if (!empty($form_data[$field])) {
				update_user_meta($user_id, $field, $form_data[$field]);
				if( $field == 'dongle_no' ) {
					update_user_meta($user_id, 'user_licenses_enabled', [$form_data[$field]]);
					update_user_meta($user_id, 'active_user_license', $form_data[$field]);
				}
			}
		}
		
		if (function_exists('um_fetch_user')) {
			um_fetch_user($user_id);
			UM()->common()->users()->send_activation($user_id, true);
		}
	}
	return $user_id;
}

function handle_registration_submission($record, $handler) {
	$form_name = $record->get_form_settings('form_id');
	$form_data = get_form_data($record);
	
	if (in_array($form_name, [FORM_IDS['CUSTOMER_REGISTRATION'], FORM_IDS['RESELLER_PARTNER'], FORM_IDS['MAKER_REGISTRATION']])) {
		if (!validate_registration($form_data, $handler)) {
			return;
		}
		
		if( $form_name == "maker_registration"){
			$form_data['account_type'] = 'maker';
		}
		
		$user_role = get_user_role($form_data['account_type'] ?? '');
		$user_id = create_wp_user($form_data, $user_role);
		
		if (is_wp_error($user_id)) {
			$handler->add_error_message(__('Error creating account. Please try again or contact support.', 'solidcam'));
		}
	} elseif ($form_name == FORM_IDS['USER_UPDATE']) {
		handle_user_update($form_data, $handler, $record);
	}
	
	$raw_fields = $record->get( 'fields' );
	$fields_to_reset = [
		'password',
		'repeat_password'
	];
	
	foreach ( $fields_to_reset as $field_id ) {
		if ( isset( $raw_fields[$field_id] ) ) {
			$raw_fields[$field_id]['value'] = '';
			$raw_fields[$field_id]['raw_value'] = '';
		}
	}
	
	$record->set( 'fields', $raw_fields );
}

function handle_user_update($form_data, $handler, $record) {
	$user_id = get_current_user_id();
	if (!$user_id) {
		$handler->add_error_message(__('User not logged in.', 'solidcam'));
		return;
	}
	
	$user = get_userdata($user_id);
	if (!empty($form_data['password']) && !wp_check_password($form_data['verify_password'], $user->user_pass, $user_id)) {
		$handler->add_error('verify_password', __('Incorrect current password.', 'solidcam'));
		return;
	}
	
	$dongle_no = get_user_meta( $user_id, 'dongle_no', true );
	$dongle_no_updated = false;
	if( isset( $form_data['dongle_no'] ) && $form_data['dongle_no'] && $form_data['dongle_no'] != $dongle_no ){
		
		$verify_dongle = verify_dongle_with_solidcam($form_data['dongle_no']);
		if (empty($verify_dongle) || isset($verify_dongle['error'])) {
			$handler->add_error('dongle_no', __('Invalid Dongle-No., Product-Key, Serial No., or Activation-ID.', 'solidcam'));
			return;
		}
		
		$dongle_no_updated = true;
	}
	
	$userdata = [
		'ID' => $user_id,
		'first_name' => $form_data['first_name'],
		'last_name' => $form_data['last_name'],
		'display_name' => "{$form_data['first_name']} {$form_data['last_name']}"
	];
	
	if (!empty($form_data['password'])) {
		$userdata['user_pass'] = $form_data['password'];
	}
	
	$user_update = wp_update_user($userdata);
	if (is_wp_error($user_update)) {
		$handler->add_error_message(__('Error updating account. Please try again.', 'solidcam'));
		return;
	}
	
	foreach (META_FIELDS as $field) {
		if (isset($form_data[$field])) {
			// Special handling for dongle_no - don't overwrite if empty
			if ($field === 'dongle_no') {
				// Only update dongle_no if it has a value and is different from existing
				// The dongle_no update logic is handled separately above
				continue;
			}
			update_user_meta($user_id, $field, $form_data[$field]);
		}
	}
	
	if( $dongle_no_updated && isset( $verify_dongle ) ){
		// Update dongle_no with the new value
		update_user_meta($user_id, 'dongle_no', $form_data['dongle_no']);
		update_user_meta($user_id, 'customer_api_response_complete', $verify_dongle);
		update_user_meta($user_id, 'customer_all_licenses', $verify_dongle['customer_all_licenses']);
		update_user_meta($user_id, 'customer_contacts', $verify_dongle['customer_contacts']);
		update_user_meta($user_id, 'customer_modules', $verify_dongle['customer_modules']);
		update_user_meta($user_id, 'customer_account', $verify_dongle['customer_account']);
		update_user_meta($user_id, 'user_licenses_enabled', [$form_data['dongle_no']]);
		update_user_meta($user_id, 'active_user_license', $form_data['dongle_no']);
	}
	
	// Clear cache when user is updated
	delete_transient('user_comparison_' . $user_id);
	delete_transient('legacy_user_' . md5($user->user_email));
	
	handle_profile_image_upload($record, $user_id);
}

function handle_profile_image_upload($record, $user_id) {
	$uploaded_files = $record->get('files');
	if (!empty($uploaded_files['profile_image']['url'][0])) {
		update_user_meta($user_id, 'wp_user_avatar', $uploaded_files['profile_image']['url'][0]);
	}
}

// Avatar handling functions
add_filter('get_avatar_url', 'custom_avatar_url', 10, 3);
add_filter('get_avatar', 'custom_avatar_html', 99999, 6);

function custom_avatar_url($url, $id_or_email, $args) {
	$user = get_user_from_identifier($id_or_email);
	if ($user && ($avatar = get_user_meta($user->ID, 'wp_user_avatar', true))) {
		return $avatar;
	}
	return $url;
}

function custom_avatar_html($avatar, $id_or_email, $size, $default, $alt, $args) {
	$user = get_user_from_identifier($id_or_email);
	if ($user && ($avatar_url = get_user_meta($user->ID, 'wp_user_avatar', true))) {
		return sprintf(
			'<img src="%s" alt="%s" width="%d" height="%d" class="avatar avatar-%d photo" />',
			esc_url($avatar_url),
			esc_attr($alt),
			(int)$size,
			(int)$size,
			(int)$size
		);
	}
	return $avatar;
}

function get_user_from_identifier($id_or_email) {
	if (is_numeric($id_or_email)) {
		return get_user_by('id', $id_or_email);
	}
	if (is_string($id_or_email)) {
		return get_user_by('email', $id_or_email);
	}
	if ($id_or_email instanceof WP_User) {
		return $id_or_email;
	}
	return false;
}

// Legacy authentication and migration with multi-role support
add_filter('authenticate', 'typo3_authenticate_user', 99, 3);

function typo3_authenticate_user($user, $username, $password) {
	if (is_a($user, 'WP_User') || empty($username) || empty($password)) {
		return $user;
	}

	global $wpdb;
	$legacy_user = $wpdb->get_row($wpdb->prepare(
		"SELECT * FROM fe_users WHERE username = %s OR email = %s",
		$username, $username
	));

	if (!$legacy_user) {
		return new WP_Error('authentication_failed', __('<strong>ERROR</strong>: Invalid email or password.', 'solidcam'));
	}

	require_once ABSPATH . WPINC . '/class-phpass.php';
	$wp_hasher = new PasswordHash(8, true);

	if (!typo3_verify_password($password, $legacy_user->password) && 
		!$wp_hasher->CheckPassword($password, $legacy_user->password)) {
		return new WP_Error('authentication_failed', __('<strong>ERROR</strong>: Invalid email or password.', 'solidcam'));
	}

	$wp_user = get_user_by('email', $legacy_user->email);
	if (!$wp_user) {
		$wp_user = migrate_legacy_user($legacy_user, $password);
	} else {
		// Update roles for existing user if needed
		$current_roles = get_user_meta($wp_user->ID, 'all_user_roles', true);
		$legacy_roles = parse_legacy_user_groups($legacy_user->usergroup);
		
		if ($current_roles !== $legacy_roles) {
			assign_multiple_roles_to_user($wp_user->ID, $legacy_roles);
		}
	}

	return $wp_user;
}

function migrate_legacy_user($legacy_user, $password) {
	// Parse multiple roles
	$roles = parse_legacy_user_groups($legacy_user->usergroup);
	$primary_role = get_primary_role($roles);
	
	$user_data = [
		'user_login' => $legacy_user->username ?: $legacy_user->email,
		'user_email' => $legacy_user->email,
		'user_pass' => $password,
		'first_name' => $legacy_user->first_name,
		'last_name' => $legacy_user->last_name,
		'display_name' => trim($legacy_user->first_name . ' ' . $legacy_user->last_name) ?: $legacy_user->name,
		'role' => $primary_role
	];

	$user_id = wp_insert_user($user_data);
	if (is_wp_error($user_id)) {
		return new WP_Error('user_creation_failed', __('<strong>ERROR</strong>: Account creation failed. Please contact support.', 'solidcam'));
	}

	// Assign multiple roles
	assign_multiple_roles_to_user($user_id, $roles);
	
	migrate_legacy_user_meta($user_id, $legacy_user);
	return get_user_by('id', $user_id);
}

function migrate_legacy_user_meta($user_id, $legacy_user) {
	$user_licenses_enabled = $legacy_user->tx_datamints_mysolidcam_unlocked_licenses ? 
		explode(",", $legacy_user->tx_datamints_mysolidcam_unlocked_licenses) : [];
	$dongle_no = $legacy_user->tx_feuserregisterextend_dongle ?? '';

	$meta_fields = [
		'telephone' => $legacy_user->telephone,
		'address' => $legacy_user->address,
		'city' => $legacy_user->city,
		'zip' => $legacy_user->zip,
		'country' => $legacy_user->country,
		'state' => $legacy_user->tx_solidcamfeuserextensions_state,
		'preferred_language' => $legacy_user->tx_solidcamfeuserextensions_preferred_language,
		'company' => $legacy_user->company,
		'user_licenses_enabled' => $user_licenses_enabled,
		'active_user_license' => $user_licenses_enabled,
		'dongle_no' => $dongle_no
	];

	if ($dongle_no) {
		$decoded_response = verify_dongle_with_solidcam($dongle_no, $user_id);
		$meta_fields = array_merge($meta_fields, [
			'customer_all_licenses' => $decoded_response['customer_all_licenses'] ?? null,
			'customer_contacts' => $decoded_response['customer_contacts'] ?? null,
			'customer_modules' => $decoded_response['customer_modules'] ?? null,
			'customer_account' => $decoded_response['customer_account'] ?? null
		]);
	}

	foreach ($meta_fields as $key => $value) {
		if (!empty($value)) {
			update_user_meta($user_id, $key, $value);
		}
	}
}

function typo3_verify_password($password, $hash) {
	return password_verify($password, $hash);
}

// Password reset handling
add_action('um_reset_password_process_hook', 'check_legacy_user_on_password_reset', 1);

function check_legacy_user_on_password_reset($args) {
	$username = '';
	foreach ($args as $key => $val) {
		if (strstr($key, 'username_b')) {
			$username = trim(sanitize_text_field($val));
			break;
		}
	}

	$user = get_user_by('login', $username) ?? get_user_by('email', $username);
	
	if (!$user) {
		global $wpdb;
		$legacy_user = $wpdb->get_row($wpdb->prepare(
			"SELECT * FROM fe_users WHERE email = %s OR username = %s",
			$username, $username
		));

		if ($legacy_user) {
			migrate_legacy_user($legacy_user, wp_generate_password());
			
			if ( username_exists( $username ) ) {
				$data = get_user_by( 'login', $username );
			} elseif ( email_exists( $user ) ) {
				$data = get_user_by( 'email', $username );
			}
			
			if ( isset( $data ) && is_a( $data, '\WP_User' ) ) {
				um_fetch_user( $data->ID );
				UM()->user()->password_reset();
			}
		}
	}
}

// Register custom user roles
add_action('after_switch_theme', 'add_custom_user_roles');

function add_custom_user_roles() {
	foreach (USER_ROLES as $role_key => $role_name) {
		add_role($role_name, ucfirst($role_name), ['read' => true]);
	}
}

/**
 * Helper Functions
 */

function user_has_role($user_id, $role) {
	$user_roles = get_user_meta($user_id, 'all_user_roles', true);
	return is_array($user_roles) && in_array($role, $user_roles);
}

function get_user_all_roles($user_id) {
	$roles = get_user_meta($user_id, 'all_user_roles', true);
	return is_array($roles) ? $roles : [];
}

/**
 * Cache Management
 */

// Clear cache when user is deleted
add_action('delete_user', 'clear_user_cache');

function clear_user_cache($user_id) {
	$user = get_userdata($user_id);
	if ($user) {
		delete_transient('user_comparison_' . $user_id);
		delete_transient('legacy_user_' . md5($user->user_email));
	}
}

// Clear all cache (admin function)
add_action('wp_ajax_clear_solidcam_cache', 'clear_all_solidcam_cache');

function clear_all_solidcam_cache() {
	if (!current_user_can('manage_options')) {
		wp_die('Insufficient permissions');
	}
	
	global $wpdb;
	$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_legacy_user_%'");
	$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_user_comparison_%'");
	
	wp_send_json_success('Cache cleared successfully');
}

/**
 * Bulk Actions for Admin
 */

// Add bulk action to sync all user roles
add_filter('bulk_actions-users', 'add_bulk_role_sync');
add_filter('handle_bulk_actions-users', 'handle_bulk_role_sync', 10, 3);

function add_bulk_role_sync($bulk_actions) {
	$bulk_actions['sync_roles'] = 'Sync Roles with TYPO3';
	return $bulk_actions;
}

function handle_bulk_role_sync($redirect_to, $doaction, $user_ids) {
	if ($doaction !== 'sync_roles') {
		return $redirect_to;
	}
	
	$synced_count = 0;
	$error_count = 0;
	
	foreach ($user_ids as $user_id) {
		$user = get_userdata($user_id);
		if (!$user) {
			$error_count++;
			continue;
		}
		
		$legacy_data = get_cached_legacy_user_data($user->user_email);
		if ($legacy_data) {
			assign_multiple_roles_to_user($user_id, $legacy_data->parsed_roles);
			delete_transient('user_comparison_' . $user_id);
			$synced_count++;
		} else {
			$error_count++;
		}
	}
	
	$redirect_to = add_query_arg([
		'synced' => $synced_count,
		'errors' => $error_count
	], $redirect_to);
	
	return $redirect_to;
}

// Show admin notices for bulk actions
add_action('admin_notices', 'show_bulk_sync_notices');

function show_bulk_sync_notices() {
	if (!empty($_REQUEST['synced'])) {
		$synced = intval($_REQUEST['synced']);
		echo '<div class="notice notice-success is-dismissible">';
		echo '<p>' . sprintf('Successfully synced roles for %d users.', $synced) . '</p>';
		echo '</div>';
	}
	
	if (!empty($_REQUEST['errors'])) {
		$errors = intval($_REQUEST['errors']);
		echo '<div class="notice notice-warning is-dismissible">';
		echo '<p>' . sprintf('Failed to sync roles for %d users (no legacy data found).', $errors) . '</p>';
		echo '</div>';
	}
}

/**
 * Admin Menu for SolidCAM Management
 */

add_action('admin_menu', 'add_solidcam_admin_menu');

function add_solidcam_admin_menu() {
	add_management_page(
		'SolidCAM User Management',
		'SolidCAM Users',
		'manage_options',
		'solidcam-users',
		'solidcam_admin_page'
	);
}

function solidcam_admin_page() {
	?>
	<div class="wrap">
		<h1>SolidCAM User Management</h1>
		
		<div class="card">
			<h2>Migration Tools</h2>
			<p>Tools for managing user migration from TYPO3 to WordPress.</p>
			
			<h3>Migrate Single User</h3>
			<form method="post" action="">
				<?php wp_nonce_field('migrate_user', 'migrate_nonce'); ?>
				<input type="email" name="user_email" placeholder="<EMAIL>" required>
				<input type="submit" name="migrate_single" value="Migrate User" class="button button-primary">
			</form>
			<!-- 
			<h3>Batch Migration</h3>
			<form method="post" action="">
				<?php wp_nonce_field('batch_migrate', 'batch_nonce'); ?>
				<p>Migrate all legacy users who don't exist in WordPress yet:</p>
				<input type="submit" name="batch_migrate" value="Start Batch Migration" class="button button-secondary" 
					   onclick="return confirm('This will migrate all legacy users. Continue?')">
			</form> -->
		</div>
		
		<div class="card">
			<h2>System Statistics</h2>
			<?php
			global $wpdb;
			
			// Get counts
			$wp_users_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->users}");
			$legacy_users_count = $wpdb->get_var("SELECT COUNT(*) FROM fe_users");
			$users_with_roles = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->usermeta} WHERE meta_key = 'all_user_roles'");
			
			// Get role distribution
			$role_stats = $wpdb->get_results("
				SELECT meta_value as roles, COUNT(*) as count 
				FROM {$wpdb->usermeta} 
				WHERE meta_key = 'all_user_roles' 
				GROUP BY meta_value
			");
			?>
			
			<table class="widefat">
				<tr><td><strong>WordPress Users:</strong></td><td><?php echo $wp_users_count; ?></td></tr>
				<tr><td><strong>Legacy TYPO3 Users:</strong></td><td><?php echo $legacy_users_count; ?></td></tr>
				<tr><td><strong>Users with Multi-Roles:</strong></td><td><?php echo $users_with_roles; ?></td></tr>
			</table>
		</div>
		
		<div class="card">
			<h2>Cache Management</h2>
			<p>Clear cached user data to force refresh of legacy user information.</p>
			<button type="button" class="button button-secondary" onclick="clearSolidCAMCache()">
				Clear All Cache
			</button>
			<span id="cache-status"></span>
		</div>
	</div>
	
	<script>
	function clearSolidCAMCache() {
		document.getElementById('cache-status').innerHTML = 'Clearing...';
		jQuery.post(ajaxurl, {
			action: 'clear_solidcam_cache'
		}, function(response) {
			if (response.success) {
				document.getElementById('cache-status').innerHTML = '<span style="color: green;">✓ Cache cleared</span>';
			} else {
				document.getElementById('cache-status').innerHTML = '<span style="color: red;">✗ Error clearing cache</span>';
			}
			setTimeout(function() {
				document.getElementById('cache-status').innerHTML = '';
			}, 3000);
		});
	}
	</script>
	<?php
	
	// Handle form submissions
	if (isset($_POST['migrate_single']) && wp_verify_nonce($_POST['migrate_nonce'], 'migrate_user')) {
		handle_single_user_migration($_POST['user_email']);
	}
	
	if (isset($_POST['batch_migrate']) && wp_verify_nonce($_POST['batch_nonce'], 'batch_migrate')) {
		handle_batch_migration();
	}
}

function handle_single_user_migration($email) {
	$legacy_data = get_cached_legacy_user_data($email);
	
	if (!$legacy_data) {
		echo '<div class="notice notice-error"><p>Legacy user not found for email: ' . esc_html($email) . '</p></div>';
		return;
	}
	
	$existing_user = get_user_by('email', $email);
	if ($existing_user) {
		echo '<div class="notice notice-warning"><p>User already exists in WordPress: ' . esc_html($email) . '</p></div>';
		return;
	}
	
	// Create legacy user object for migration
	global $wpdb;
	$legacy_user = $wpdb->get_row($wpdb->prepare("SELECT * FROM fe_users WHERE email = %s", $email));
	
	if ($legacy_user) {
		$result = migrate_legacy_user($legacy_user, wp_generate_password());
		if (!is_wp_error($result)) {
			echo '<div class="notice notice-success"><p>Successfully migrated user: ' . esc_html($email) . '</p></div>';
		} else {
			echo '<div class="notice notice-error"><p>Migration failed: ' . $result->get_error_message() . '</p></div>';
		}
	}
}

function handle_batch_migration() {
	global $wpdb;
	
	// Get all legacy users who don't exist in WordPress
	$legacy_users = $wpdb->get_results("
		SELECT fu.* FROM fe_users fu 
		LEFT JOIN {$wpdb->users} wu ON fu.email = wu.user_email 
		WHERE wu.user_email IS NULL 
		AND fu.email != '' 
		LIMIT 50
	");
	
	$migrated = 0;
	$errors = 0;
	
	foreach ($legacy_users as $legacy_user) {
		$result = migrate_legacy_user($legacy_user, wp_generate_password());
		if (!is_wp_error($result)) {
			$migrated++;
		} else {
			$errors++;
		}
	}
	
	echo '<div class="notice notice-success"><p>Batch migration completed: ' . $migrated . ' users migrated, ' . $errors . ' errors.</p></div>';
}

/**
 * User Profile Display
 */

add_action('show_user_profile', 'show_solidcam_user_profile_fields');
add_action('edit_user_profile', 'show_solidcam_user_profile_fields');

function show_solidcam_user_profile_fields($user) {
	$comparison = get_user_comparison_data($user->ID);
	$all_roles = get_user_all_roles($user->ID);
	
	?>
	<h3>SolidCAM User Information</h3>
	<table class="form-table">
		<tr>
			<th><label>Legacy Status</label></th>
			<td>
				<?php if ($comparison['legacy_exists']): ?>
					<span style="color: green;">✓ Found in TYPO3</span>
					<br><small>TYPO3 UserGroup: <code><?php echo esc_html($comparison['legacy_usergroup']); ?></code></small>
				<?php else: ?>
					<span style="color: orange;">⚠ Not found in TYPO3</span>
				<?php endif; ?>
			</td>
		</tr>
		
		<tr>
			<th><label>WordPress Roles</label></th>
			<td>
				<strong>Primary:</strong> <?php echo esc_html($comparison['wp_primary_role']); ?><br>
				<?php if (!empty($all_roles)): ?>
					<strong>All Roles:</strong> <?php echo esc_html(implode(', ', $all_roles)); ?>
				<?php endif; ?>
			</td>
		</tr>
		
		<?php if ($comparison['legacy_exists']): ?>
		<tr>
			<th><label>TYPO3 Roles</label></th>
			<td>
				<strong>Primary:</strong> <?php echo esc_html($comparison['legacy_primary_role']); ?><br>
				<strong>All Roles:</strong> <?php echo esc_html(implode(', ', $comparison['legacy_roles'])); ?>
			</td>
		</tr>
		
		<tr>
			<th><label>Role Synchronization</label></th>
			<td>
				<?php if ($comparison['roles_match']): ?>
					<span style="color: green;">✓ Roles are synchronized</span>
				<?php else: ?>
					<span style="color: red;">✗ Roles are not synchronized</span>
					<br><button type="button" class="button button-secondary" onclick="syncUserRoles(<?php echo $user->ID; ?>)">
						Sync Roles Now
					</button>
				<?php endif; ?>
			</td>
		</tr>
		<?php endif; ?>
		
		<tr>
			<th><label>Dongle Information</label></th>
			<td>
				<?php 
				$dongle = get_user_meta($user->ID, 'dongle_no', true);
				if ($dongle): ?>
					<code><?php echo esc_html($dongle); ?></code>
				<?php else: ?>
					<em>No dongle assigned</em>
				<?php endif; ?>
			</td>
		</tr>
	</table>
	<?php
}