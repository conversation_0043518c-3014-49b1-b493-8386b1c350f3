<?php

class SalesForceUserGroupService {
	protected $defaultUsergroup = 1;

	protected $salesforceMapping = [
		'Valid' => '3',
		'Expired' => '1',
		'Unknown' => '1',
	];

	protected $fegroupUidsDoNotTouch = [4, 5, 62, 2];

	protected $salesForceRefreshToken = '***************************************************************************************';
	protected $salesforceConsumerKey = '3MVG9WtWSKUDG.x6iIGrpPBJuD0bkeciZ5DYI.aKWhgXtZLudeNZeN.CIfiog5WS1YM9O3k7n3W.zhh20f0Mg';
	protected $salesforceConsumerSecret = '4634365442185483195';
	protected $salesforceRedirectUrl = 'https://isolidcam.com/salesforcetokenapp.php';

	protected $dongleField = 'dongle_number';
	protected $hardwareDongleLength = 5;

	public function getSalesForceInformations($user) {
		$dongleNumber = preg_replace('/[^a-zA-Z0-9\-]*/', '', $user[$this->dongleField]);
		
		if ($dongleNumber) {
			list($salesForceAccessToken, $salesForceInstanceURL) = $this->auth($this->salesForceRefreshToken);

			if (!$salesForceAccessToken || !$salesForceInstanceURL) {
				throw new \Exception('Did not receive any access token');
			}

			$query = (strlen(trim($dongleNumber)) <= $this->hardwareDongleLength)
				? "SELECT Dongle__c.New_Maintenance_End_Date__c, Dongle__c.Active_Key__c, Dongle__c.SubReseller__c FROM Dongle__c WHERE Dongle__c.Name='" . $dongleNumber . "'"
				: "SELECT New_Maintenance_End_Date__c, Active_Key__c, SubReseller__c FROM Dongle__c WHERE Name='" . $dongleNumber . "' OR Product_key__r.Name='" . $dongleNumber . "'";

			$data = $this->query($salesForceAccessToken, $salesForceInstanceURL, $query);
			
			$dongle = isset( $data->records[0] ) ? $data->records[0] : [];
			
			if( !empty( $dongle ) ){
				if ($dongle->New_Maintenance_End_Date__c < date('Y-m-d') || $dongle->Active_Key__c != 1) {
					return ['type' => 'Expired'];
				}
				
				return ['type' => 'Valid'];
			}
		}

		return ['type' => 'Unknown'];
	}

	protected function auth($refreshToken) {
		$tokenFields = [
			'grant_type' => 'refresh_token',
			'client_id' => $this->salesforceConsumerKey,
			'client_secret' => $this->salesforceConsumerSecret,
			'refresh_token' => $refreshToken,
		];
		
		$response = wp_remote_post('https://login.salesforce.com/services/oauth2/token', [
			'body' => $tokenFields,
		]);

		if (is_wp_error($response)) {
			throw new \Exception('Failed to authenticate with Salesforce.');
		}

		$data = json_decode(wp_remote_retrieve_body($response), true);
		
		return [$data['access_token'] ?? null, $data['instance_url'] ?? null];
	}

	protected function query($token, $instance, $query) {
		$response = wp_remote_get(
			$instance . '/services/data/v33.0/query?' . http_build_query(['q' => $query]),
			[
				'headers' => [
					'Authorization' => 'Bearer ' . $token,
				],
			]
		);

		if (is_wp_error($response)) {
			throw new \Exception('Failed to execute Salesforce query.');
		}

		return json_decode(wp_remote_retrieve_body($response));
	}
}
