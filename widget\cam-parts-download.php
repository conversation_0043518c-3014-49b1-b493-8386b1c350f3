<?php

class Elementor_Cam_Parts_Widget extends \Elementor\Widget_Base {

	public function get_name() {
		return 'elementor_cam_parts';
	}

	public function get_title() {
		return __( 'Cam Part Download', 'elementor-cam-parts' );
	}

	public function get_icon() {
		return 'eicon-library-download';
	}

	public function get_categories() {
		return [ 'general' ];
	}

	protected function _register_controls() {

		$this->start_controls_section(
			'section_content',
			[
				'label' => __( 'Cam Parts', 'elementor-cam-parts' ),
			]
		);

		$parts = get_field('parts_details', 'option'); // Get ACF data from options page

		if( $parts ) {
			$software_options = [];
			foreach( $parts as $index => $part ) {
				$versions = $part['software_version'];
				$counts = 0;
				if ($versions) {
					foreach ($versions as $version) {
						if( $counts < 1 ){
							$part['software_name'] = str_replace("{year}", $version['year'], $part['software_name']);
						}
						$counts++;
					}
				}
				$software_options[$index] = $part['software_name'];
			}

			$this->add_control(
				'software_select',
				[
					'label' => __( 'Select Software', 'elementor-cam-parts' ),
					'type' => \Elementor\Controls_Manager::SELECT,
					'options' => $software_options,
				]
			);
		}

		// Button styling options
		$this->add_control(
			'button_icon',
			[
				'label' => __( 'Button Icon', 'elementor-cam-parts' ),
				'type' => \Elementor\Controls_Manager::ICONS,
			]
		);

		$this->add_control(
			'button_background',
			[
				'label' => __( 'Button Background Color', 'elementor-cam-parts' ),
				'type' => \Elementor\Controls_Manager::COLOR,
				'selectors' => [
					'{{WRAPPER}} .download-button' => 'background-color: {{VALUE}}',
				],
			]
		);

		$this->end_controls_section();
	}

	protected function render() {
		$settings = $this->get_settings_for_display();
		$parts = get_field('parts_details', 'option'); // Fetch the ACF parts data
		$software_index = $settings['software_select'];

		if (isset($parts[$software_index])) {
			$selected_software = $parts[$software_index];
			$software_name = $selected_software['software_name'];
			$software_language = $selected_software['software_language'];
			$versions = $selected_software['software_version'];
			$counts = 0;
			$all_version_options = '';
			$software_name_original = $software_name;
			if ($versions) {
				foreach ($versions as $version) {
					if( $counts < 1 ){
						$software_name = str_replace("{year}", $version['year'], $software_name);
					}
					$all_version_options .= '<option value="' . esc_url($version['download_link']) . '">' . esc_html($version['year']) . '</option>';
					$counts++;
				}
			}
			
			echo '<div class="software-details">';
			echo '<h4 class="year-display" data-original-text="' . $software_name_original . '">' . esc_html($software_name) . '</h4>';
			echo '<div class="middle-content">';
			    echo '<p class="languages"> Language: ' . esc_html($software_language) . '</p>';

    			if( isset( $versions[0] ) ){
    				echo '<a href="' . esc_url($versions[0]['download_link']) . '" class="download-button" target="_blank">';
    				\Elementor\Icons_Manager::render_icon( $settings['button_icon'], [ 'aria-hidden' => 'true' ] );
    				echo '</a>';
    			}
    		    echo '</div>';
    		    echo '<div class="footer-content">';
    			
        			if (!empty($all_version_options)) {
        				echo '<select class="version-select">';
        				echo $all_version_options;
        				echo '</select>';
        			}
    			echo '</div>';
		
			echo '</div>';
		}
	}
}
