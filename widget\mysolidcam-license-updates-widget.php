<?php

if (!defined('ABSPATH')) exit; // Exit if accessed directly

class Custom_License_Updates_Widget extends \Elementor\Widget_Base {

	public function get_name() {
		return 'license_updates';
	}

	// Widget title
	public function get_title() {
		return __('License Updates', 'solidcame');
	}

	// Widget icon
	public function get_icon() {
		return 'eicon-refresh';
	}

	// Widget category
	public function get_categories() {
		return ['general'];
	}

	// Render the widget output on the frontend
	protected function render() {
		$current_user = wp_get_current_user();

		// Ensure the user is logged in
		if (!$current_user->exists()) {
			echo '<p>' . esc_html__('Please log in to view license updates.', 'solidcame') . '</p>';
			return;
		}

		$user_id = $current_user->ID;

		// Fetch active license and customer licenses from user meta
		$active_license = get_user_meta($user_id, 'active_user_license', true);
		$customer_all_licenses = get_user_meta($user_id, 'customer_all_licenses', true);

		// Validate the active license and licenses array
		if (empty($active_license) || empty($customer_all_licenses) || !is_array($customer_all_licenses)) {
			echo '<p>' . esc_html__('No active license or updates available.', 'solidcame') . '</p>';
			return;
		}

		// Find the active license in the licenses array
		$matched_license = array_filter($customer_all_licenses, function ($license) use ($active_license) {
			return isset($license['license_number']) && $license['license_number'] == $active_license;
		});

		// Validate if the active license exists
		$matched_license = reset($matched_license); // Get the first match
		if (empty($matched_license)) {
			echo '<p>' . esc_html__('No updates available for your active license.', 'solidcame') . '</p>';
			return;
		}

		// Extract details of the matched license
		$license_number = esc_html($matched_license['license_number']);
		$license_file_url = esc_url($matched_license['license_file']);

		// Render the widget
		echo '<div class="license-updates-widget">';
		echo '<h4>' . esc_html__('License Updates', 'solidcame') . '</h4>';
		echo '<p>' . sprintf(
			esc_html__('Available Update for License-No.: %s', 'solidcame'),
			'<strong>' . $license_number . '</strong>'
		) . '</p>';
		echo '<a href="' . $license_file_url . '" target="_blank" class="button"> <i class="fa-light fa-arrow-down-to-line icon-left me-1" aria-hidden="true"></i>';
		echo sprintf(esc_html__('Download license update (%d)', 'solidcam'), $license_number);
		echo '</a>';
		echo '</div>';
	}
}
