<?php

// add_action( 'elementor/frontend/widget/before_render', 'solidcam_call_to_action_add_accordion_before_render', 10, 1 );
// add_action( 'elementor/widget/before_render_content', 'solidcam_call_to_action_add_accordion_before_render', 10, 1 );
add_filter( 'elementor/widget/render_content', 'solidcam_call_to_action_add_accordion_render_content', 10, 2 );

function solidcam_call_to_action_add_accordion_render_content( $widget_content, $object ) {
    if( $object->get_name() == 'nested-accordion' ) {
        
        $setting_items = $object->get_settings('items');
        
        // Check if the expand option is enabled
        if ( $object->get_settings('enable_accordion_expand') == 'yes' ) {
            // Modify the content to ensure all accordion items are open by default
            $widget_content = preg_replace_callback(
                '/<details([^>]*)>(.*?)<\/details>/s', 
                function($matches) {
                    // Add the 'open' attribute to <details> and set aria-expanded to true
                    $details_opened = str_replace('<details', '<details open', $matches[0]);
                    $summary_opened = preg_replace('/<summary([^>]*)aria-expanded="false"/', '<summary$1aria-expanded="true"', $details_opened);
                    return $summary_opened;
                }, 
                $widget_content
            );
        }
    }
    
    return $widget_content;
}

add_action( 'elementor/element/nested-accordion/section_interactions/before_section_end', 'solidcam_call_to_action_add_accordion', 10, 2 );
function solidcam_call_to_action_add_accordion($element, $args)
{
	$element->add_control('enable_accordion_expand', [
		'label' => 'Expand All Accordions',
		'type' => Elementor\Controls_Manager::SWITCHER,
		'default' => 'no',
	]);
}
?>