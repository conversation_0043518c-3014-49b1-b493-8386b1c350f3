<?php
// Hook into user login action (existing code)
add_action('wp_login', 'update_user_meta_on_login_from_api', 10, 2);

// NEW: Hook into user profile update in WordPress admin
add_action('personal_options_update', 'update_user_meta_on_admin_save');
add_action('edit_user_profile_update', 'update_user_meta_on_admin_save');

function update_user_meta_on_login_from_api($user_login, $current_user) {
	// Get user ID
	$user_id = $current_user->ID;
	// Fetch the active_user_license from user meta
	$dongle_no = get_user_meta($user_id, 'dongle_no', true);
	
	// If no active license is set, do nothing
	if (empty($dongle_no)) {
		return;
	}
	
	// Call the common function to update user meta
	update_solidcam_user_data($user_id, $dongle_no);
}

/**
 * Update user meta when user profile is saved in WordPress admin
 *
 * @param int $user_id The user ID being updated
 */
function update_user_meta_on_admin_save($user_id) {
	// Check if current user has permission to edit this user
	if (!current_user_can('edit_user', $user_id)) {
		return;
	}
	
	// Get the dongle number from user meta
	$dongle_no = get_user_meta($user_id, 'dongle_no', true);
	
	// If no dongle number is set, do nothing
	if (empty($dongle_no)) {
		return;
	}
	
	// Call the common function to update user meta
	update_solidcam_user_data($user_id, $dongle_no);
}

/**
 * Common function to update SolidCAM user data
 *
 * @param int $user_id The user ID
 * @param string $dongle_no The dongle number
 */
function update_solidcam_user_data($user_id, $dongle_no) {
	$decoded_response = verify_dongle_with_solidcam($dongle_no, $user_id);
	
	// Check if the response is valid (not a WP_Error)
	if (is_wp_error($decoded_response)) {
		error_log("Failed to update SolidCAM data for user ID {$user_id}: " . $decoded_response->get_error_message());
		return;
	}
	
	/* if(
		in_array('customer', $current_user->roles)
		&& isset( $decoded_response['customer_account']) 
		&& !empty( $decoded_response['customer_account']) 
		&& isset( $decoded_response['customer_account']['customer_on_sub'] ) 
		&& !$decoded_response['customer_account']['customer_on_sub']
	){
		$meta_fields['subscription_status'] = 'false';
	} else {
		$meta_fields['subscription_status'] = 'true'; 
	} */
	
	// Store the response in user meta
	update_user_meta($user_id, 'customer_api_response_complete', $decoded_response);
	
	// Only update if the data exists to avoid overwriting with empty values
	if (isset($decoded_response['customer_all_licenses'])) {
		update_user_meta($user_id, 'customer_all_licenses', $decoded_response['customer_all_licenses']);
	}
	
	if (isset($decoded_response['customer_contacts'])) {
		update_user_meta($user_id, 'customer_contacts', $decoded_response['customer_contacts']);
	}
	
	if (isset($decoded_response['customer_modules'])) {
		update_user_meta($user_id, 'customer_modules', $decoded_response['customer_modules']);
	}
	
	if (isset($decoded_response['customer_account'])) {
		update_user_meta($user_id, 'customer_account', $decoded_response['customer_account']);
	}
	
	// Optionally log success
	error_log("SolidCAM user meta updated successfully for user ID {$user_id}");
}

/**
 * Verify dongle using SolidCAM API.
 *
 * @param string $dongle_no The dongle number, product key, serial number, or activation ID.
 * @param int|null $user_id Optional. The WordPress user ID for logging purposes.
 * @return array|WP_Error The decoded API response on success, or a WP_Error object on failure.
 */
function verify_dongle_with_solidcam($dongle_no, $user_id = null) {
	// Construct the API URL
	$api_url = "https://solid.my.salesforce-sites.com/api/services/apexrest/GetCustomerData?lic=" . urlencode($dongle_no);
	
	// Use WordPress's HTTP API to fetch the data
	$response = wp_remote_get($api_url, array(
		'timeout' => 30, // Timeout for the request
		'headers' => array(
			'Cache-Control' => 'no-cache',
		),
	));
	
	// Check for WP_Error
	if (is_wp_error($response)) {
		$error_message = $response->get_error_message();
		error_log("API request failed for user ID {$user_id}: {$error_message}");
		return new WP_Error('api_request_failed', __("API request failed: {$error_message}"));
	}
	
	// Check for a valid response code
	$response_code = wp_remote_retrieve_response_code($response);
	if ($response_code !== 200) {
		error_log("API returned a non-200 status code for user ID {$user_id}: {$response_code}");
		return new WP_Error('api_invalid_response_code', __("API returned a non-200 status code: {$response_code}"));
	}
	
	// Decode the JSON response
	$body = wp_remote_retrieve_body($response);
	$body = preg_replace('/<response>"/', '"</response>', $body);
	$clean_json = stripslashes($body);
	$clean_json = trim($clean_json, '"');
	$decoded_response = json_decode($clean_json, true);
	
	// Validate the response
	if (!is_array($decoded_response)) {
		error_log("Invalid API response for user ID {$user_id}: " . $body);
		return new WP_Error('api_invalid_response', __("Invalid API response: {$body}"));
	}
	
	// Return the decoded response
	return $decoded_response;
}