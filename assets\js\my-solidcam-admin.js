jQuery(document).ready(function ($) {
	// Monitor changes in the choose_license field
	$(document).on('change', '[data-name="choose_license"] select', function () {
		let selectedLicense = $(this).val(); // Get the selected license
		let $modulesField = $('[data-name="choose_modules"] select');
		let user_id = $('[name="user_id"]').val();
		if (selectedLicense) {
			// Fetch the corresponding modules via AJAX
			$.ajax({
				url: ajaxurl, // WordPress AJAX URL
				type: 'POST',
				data: {
					action: 'get_modules_by_license',
					license: selectedLicense,
					user_id: user_id
				},
				success: function (response) {
					// Populate the choose_modules field with matching modules
					if (response.success && response.data.modules) {
						
						// Get all options from the modules field
						$modulesField.find('option').each(function () {
							const $option = $(this);
							const value = $option.val();
				
							// Check if the value matches one of the modules from the response
							if (response.data.modules.includes(value)) {
								$option.prop('selected', true); // Select the matching option
							} else {
								$option.prop('selected', false); // Deselect if not matching
							}
						});
				
						$modulesField.trigger('change'); // Trigger change event for ACF
					} else {
						$modulesField.find('option').each(function () {
							const $option = $(this);
							$option.prop('selected', false);
						});
						
						$modulesField.trigger('change'); // Trigger change event for ACF
					}
				},
			});
		}
	});
	

        
	
	
});
