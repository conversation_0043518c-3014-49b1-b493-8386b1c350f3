<?php

if (!class_exists('Customer_Reviews_Rating_Count')) {
    class Customer_Reviews_Rating_Count extends \Elementor\Core\DynamicTags\Tag {
        public function get_name() {
            return 'Customer-Reviews-Rating-Count';
        }

        public function get_title() {
            return 'Customer Reviews Rating Count';
        }

        public function get_group() {
            return 'site';
        }

        public function get_categories() {
            return [ \Elementor\Modules\DynamicTags\Module::TEXT_CATEGORY ];
        }

        protected function register_controls() {
            $post_types = get_post_types(['public' => true], 'objects');
            $options = [];
            
            foreach ($post_types as $post_type) {
                $options[$post_type->name] = $post_type->label;
            }

            $this->add_control(
                'selected_cpt',
                [
                    'label' => 'Select Post Type',
                    'type' => \Elementor\Controls_Manager::SELECT,
                    'default' => 'post',
                    'options' => $options,
                ]
            );
        }

        public function render() {
            $selected_cpt = $this->get_settings('selected_cpt');
            
            if (!$selected_cpt) {
                echo '<div>' . __('Please select a post type.', 'solidcam') . '</div>';
                return;
            }

            $count = wp_count_posts($selected_cpt);
            $total_count = isset($count->publish) ? $count->publish : 0;
            
            // Get all posts of the selected CPT
            $args = [
                'post_type'      => $selected_cpt,
                'post_status'    => 'publish',
                'posts_per_page' => -1,
                'fields'         => 'ids'
            ];
            
            $posts = get_posts($args);
            $total_rating = 0;
            $rating_count = 0;
            
            foreach ($posts as $post_id) {
                $rating = get_post_meta($post_id, 'customer_rating', true);
                if (!empty($rating)) {
                    $total_rating += floatval($rating);
                    $rating_count++;
                }
            }
            
            $average_rating = ($rating_count > 0) ? round($total_rating / $rating_count, 2) : 0;
            
            echo '<div class="post-total-count-wrp">';
            echo '<div class="post-total-count">' . esc_html($total_count) . ' ' . __('Total Ratings', 'solidcam') . '</div>';
            echo '<div class="post-total-stars">' . esc_html($average_rating) . ' ' . __('Stars out of 5', 'solidcam') . '</div>';
            echo '</div>';
        }
    }
}

?>
