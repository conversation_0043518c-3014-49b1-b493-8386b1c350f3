<?php
/**
 * SolidCAM Website User Management
 * Handles user registration, authentication, and password recovery
 */

// Handle Elementor form submissions
add_action('elementor_pro/forms/new_record', 'handle_registration_submission', 10, 2);

function handle_registration_submission($record, $handler) {
	$form_name = $record->get_form_settings('form_id');
	// $form_data = $record->get_formatted_data();
	
	$raw_fields = $record->get('fields');
	$form_data = [];
	foreach ($raw_fields as $field) {
		$form_data[$field['id']] = $field['value'];
	}
	
	if ($form_name == 'customer_registration' || $form_name == 'reseller_partner') {
		// User Registration Logic (Same as Before)
		
		// Get form fields
		$email = $form_data['email'];
		$password = $form_data['password'];
		$repeat_password = $form_data['repeat_password'];
		$first_name = $form_data['first_name'];
		$last_name = $form_data['last_name'];
		$company = $form_data['company'];
		$address = $form_data['address'];
		$zip = $form_data['zip'];
		$city = $form_data['city'];
		$telephone = $form_data['telephone'];
		$country = $form_data['country'];
		$dongle_no = $form_data['dongle_no'];
		$account_type = isset($form_data['account_type']) ? $form_data['account_type'] : '';
	
		$error_found = false;
	
		// Validate password match
		if ($password !== $repeat_password) {
			$handler->add_error('password', esc_html__('Passwords do not match. Please try again.', 'solidcam'));
			$handler->add_error('repeat_password', '');
			$error_found = true;
		}
	
		// Check if user exists in WordPress
		if (email_exists($email)) {
			$handler->add_error('email', esc_html__('This email address is already registered. Please use a different email or reset your password.', 'solidcam'));
			$error_found = true;
		}
	
		// Check if user exists in legacy database (TYPO3 fe_users)
		global $wpdb;
		$legacy_user = $wpdb->get_row($wpdb->prepare("SELECT * FROM fe_users WHERE email = %s", $email));
	
		if ($legacy_user) {
			$handler->add_error('email', esc_html__('This email address is already registered. Please use a different email or reset your password.', 'solidcam'));
			$error_found = true;
		}
	
		// Verify Dongle Key
		$verify_dongle = verify_dongle_with_solidcam($dongle_no);
		if (empty($verify_dongle) || isset($verify_dongle['error'])) {
			$handler->add_error('dongle_no', esc_html__('Invalid Dongle-No., Product-Key, Serial No., or Activation-ID.', 'solidcam'));
			$error_found = true;
		}
	
		if ($error_found) {
			return;
		}
	
		// Assign user role based on account type
		$user_role = match ($account_type) {
			'staff' => 'staff',
			'reseller' => 'reseller',
			'partner' => 'partner',
			default => 'customer'
		};
	
		// Set subscription status for customers
		if ($user_role == 'customer' && !empty($verify_dongle['customer_account']['customer_on_sub'])) {
			$meta_fields['subscription_status'] = 'false';
		}
	
		// Create user in WordPress
		$userdata = array(
			'user_login'   => $email,
			'user_email'   => $email,
			'user_pass'    => $password,
			'first_name'   => $first_name,
			'last_name'    => $last_name,
			'display_name' => $first_name . ' ' . $last_name,
			'role'         => $user_role
		);
	
		$user_id = wp_insert_user($userdata);
	
		if (is_wp_error($user_id)) {
			$handler->add_error_message('Error creating account. Please try again or contact support.');
			return;
		}
	
		// Save user meta fields
		$meta_fields = compact('company', 'address', 'zip', 'city', 'telephone', 'country', 'dongle_no');
		foreach ($meta_fields as $key => $value) {
			update_user_meta($user_id, $key, $value);
		}
	
		if (function_exists('um_fetch_user')) {
			um_fetch_user($user_id);
			UM()->common()->users()->send_activation($user_id, true);
		}
	} elseif ($form_name == 'user_update_form') {
		// User Update Logic (New)
	
		// Get the currently logged-in user
		$user_id = get_current_user_id();
		if (!$user_id) {
			$handler->add_error_message('User not logged in.');
			return;
		}
	
		// Get form fields
		$password = $form_data['password'];
		$verify_password = $form_data['verify_password']; // User enters current password
		$first_name = $form_data['first_name'];
		$last_name = $form_data['last_name'];
		$company = $form_data['company'];
		$address = $form_data['address'];
		$zip = $form_data['zip'];
		$city = $form_data['city'];
		$telephone = $form_data['telephone'];
		$country = $form_data['country'];
		$dongle_no = $form_data['dongle_no'];
	
		$error_found = false;
	
		// Verify the current password before updating
		$user = get_userdata($user_id);
		if ( !empty($password) && !wp_check_password($verify_password, $user->user_pass, $user_id)) {
			$handler->add_error('verify_password', esc_html__('Incorrect current password.', 'solidcam'));
			$handler->add_error('password', '');
			$error_found = true;
		}
	
		// Verify the Dongle Key
		$verify_dongle = verify_dongle_with_solidcam($dongle_no);
		if (empty($verify_dongle) || isset($verify_dongle['error'])) {
			$handler->add_error('dongle_no', esc_html__('Invalid Dongle-No., Product-Key, Serial No., or Activation-ID.', 'solidcam'));
			$error_found = true;
		}
	
		if ($error_found) {
			return;
		}
	
		// Update User Data
		$userdata = array(
			'ID'         => $user_id,
			'first_name' => $first_name,
			'last_name'  => $last_name,
			'display_name' => $first_name . ' ' . $last_name
		);
	
		// Update password if provided
		if (!empty($password)) {
			$userdata['user_pass'] = $password;
		}
	
		$user_update = wp_update_user($userdata);
		if (is_wp_error($user_update)) {
			$handler->add_error_message('Error updating account. Please try again.');
			return;
		}
	
		// Update user meta
		$meta_fields = compact('company', 'address', 'zip', 'city', 'telephone', 'country', 'dongle_no');
		foreach ($meta_fields as $key => $value) {
			update_user_meta($user_id, $key, $value);
		}
		
		$uploaded_files = $record->get('files');
		if (!empty($uploaded_files['profile_image'])) {
			$file_info = $uploaded_files['profile_image'];
			if (!empty($file_info['url'])) {
				$profile_image_url = $file_info['url'][0];
				update_user_meta($user_id, 'wp_user_avatar', $profile_image_url);
			}
		}
	}
}

add_filter('get_avatar_url', function ($url, $id_or_email, $args) {
	$user = false;

	if (is_numeric($id_or_email)) {
		$user = get_user_by('id', $id_or_email);
	} elseif (is_string($id_or_email)) {
		$user = get_user_by('email', $id_or_email);
	} elseif ($id_or_email instanceof WP_User) {
		$user = $id_or_email;
	}

	if ($user) {
		$user_id = $user->ID;

		//Retrieve the profile image uploaded via Elementor
		$profile_image_url = get_user_meta($user_id, 'wp_user_avatar', true);

		if (!empty($profile_image_url)) {
			return $profile_image_url; // Override Gravatar with Elementor uploaded image
		}
	}

	return $url; // Fallback to default Gravatar if no custom image is set
}, 10, 3);

add_filter('get_avatar', function ($avatar, $id_or_email, $size, $default, $alt, $args) {
	$user = false;

	if (is_numeric($id_or_email)) {
		$user = get_user_by('id', $id_or_email);
	} elseif (is_string($id_or_email)) {
		$user = get_user_by('email', $id_or_email);
	} elseif ($id_or_email instanceof WP_User) {
		$user = $id_or_email;
	}

	if ($user) {
		$user_id = $user->ID;

		// Get the uploaded Elementor profile image
		$profile_image_url = get_user_meta($user_id, 'wp_user_avatar', true);

		if (!empty($profile_image_url)) {
			return "<img src='{$profile_image_url}' alt='{$alt}' width='{$size}' height='{$size}' class='avatar avatar-{$size} photo' />";
		}
	}

	return $avatar; // Fallback to default WordPress avatar
}, 99999, 6);


// Keep existing legacy authentication function
function typo3_authenticate_user($user, $username, $password) {
	if (is_a($user, 'WP_User')) {
		return $user;
	}

	if (empty($username) || empty($password)) {
		return null;
	}

	global $wpdb;
	$legacy_user = $wpdb->get_row($wpdb->prepare(
		"SELECT * FROM fe_users WHERE username = %s OR email = %s",
		$username, $username
	));
	
	

	if ($legacy_user) {
		require_once ABSPATH . WPINC . '/class-phpass.php';
		$wp_hasher = new PasswordHash(8, TRUE);

		if (typo3_verify_password($password, $legacy_user->password) || $wp_hasher->CheckPassword($password, $legacy_user->password)) {
			
			
			
			// Check if user exists in WordPress
			$wp_user = get_user_by('email', $legacy_user->email);

			if (!$wp_user) {
				
				$user_role = 'customer';
				if( $legacy_user->usergroup == 4 ){
					$user_role = 'staff';
				} else if( $legacy_user->usergroup == 5 ){
					$user_role = 'reseller';
				} else if( $legacy_user->usergroup == 16 ){
					$user_role = 'partner';
				}
				
				// Create WordPress user from legacy data
				$user_data = array(
					'user_login' => ( $legacy_user->username ) ? $legacy_user->username : $legacy_user->email,
					'user_email' => $legacy_user->email,
					'user_pass'  => $password,
					'first_name' => $legacy_user->first_name,
					'last_name'  => $legacy_user->last_name,
					'display_name' => $legacy_user->name,
					'role'      => $user_role
				);

				$user_id = wp_insert_user($user_data);

				if (is_wp_error($user_id)) {
					return new WP_Error('user_creation_failed', __('<strong>ERROR</strong>: Account creation failed. Please contact support.'));
				}
				
				$user_licenses_enabled = $legacy_user->tx_datamints_mysolidcam_unlocked_licenses ? explode( ",", $legacy_user->tx_datamints_mysolidcam_unlocked_licenses ) : [];
				$dongle_no = $legacy_user->tx_feuserregisterextend_dongle ? $legacy_user->tx_feuserregisterextend_dongle : [];
				
				// Copy legacy meta data
				$meta_fields = array(
					'telephone' => $legacy_user->telephone,
					'address' => $legacy_user->address,
					'city' => $legacy_user->city,
					'zip' => $legacy_user->zip,
					'country' => $legacy_user->country,
					'company' => $legacy_user->company,
					'user_licenses_enabled' => $user_licenses_enabled,
					'dongle_no' => $dongle_no
				);
				
				if( !empty( $dongle_no ) ){
					$decoded_response = verify_dongle_with_solidcam( $dongle_no, $user_id);
					
					$meta_fields['customer_all_licenses'] = $decoded_response['customer_all_licenses'];
					$meta_fields['customer_contacts'] = $decoded_response['customer_contacts'];
					$meta_fields['customer_modules'] = $decoded_response['customer_modules'];
					$meta_fields['customer_account'] = $decoded_response['customer_account'];
				}
				
				foreach ($meta_fields as $key => $value) {
					if (!empty($value)) {
						update_user_meta($user_id, $key, $value);
					}
				}

				$wp_user = get_user_by('id', $user_id);
			}

			return $wp_user;
		}
	}

	return new WP_Error('authentication_failed', __('<strong>ERROR</strong>: Invalid email or password.'));
}
add_filter('authenticate', 'typo3_authenticate_user', 99, 3);

function typo3_verify_password($password, $hash) {
	return password_verify($password, $hash);
}

// Handle password reset
add_action('um_reset_password_process_hook', 'check_legacy_user_on_password_reset', 1, 1);

function check_legacy_user_on_password_reset( $args ) {
	$user = null;
	
	foreach ( $args as $key => $val ) {
		if ( strstr( $key, 'username_b' ) ) {
			$user = trim( sanitize_text_field( $val ) );
		}
	}
	
	if ( username_exists( $user ) ) {
		$data = get_user_by( 'login', $user );
	} elseif ( email_exists( $user ) ) {
		$data = get_user_by( 'email', $user );
	}
	
	// If no WordPress user found, check legacy database
	if ( !isset( $data ) || !is_a( $data, '\WP_User' ) ) {
		global $wpdb;
		$legacy_user = $wpdb->get_row($wpdb->prepare(
			"SELECT * FROM fe_users WHERE email = %s OR username = %s",
			$user, $user
		));
		
		if ($legacy_user) {
			// Create WordPress user from legacy data
			$temp_password = wp_generate_password();
			
			$user_role = 'customer';
			if( $legacy_user->usergroup == 4 ){
				$user_role = 'staff';
			} else if( $legacy_user->usergroup == 5 ){
				$user_role = 'reseller';
			} else if( $legacy_user->usergroup == 16 ){
				$user_role = 'partner';
			}
			
			$userdata = array(
				'user_login' => ( $legacy_user->username ) ? $legacy_user->username : $legacy_user->email,
				'user_email'    => $legacy_user->email,
				'first_name'    => $legacy_user->first_name,
				'last_name'     => $legacy_user->last_name,
				'display_name'  => $legacy_user->name,
				'user_pass'     => $temp_password,
				'role'          => $user_role
			);
			
			$user_id = wp_insert_user($userdata);
			if (!is_wp_error($user_id)) {
				
				$user_licenses_enabled = $legacy_user->tx_datamints_mysolidcam_unlocked_licenses ? explode( ",", $legacy_user->tx_datamints_mysolidcam_unlocked_licenses ) : [];
				$dongle_no = $legacy_user->tx_feuserregisterextend_dongle ? $legacy_user->tx_feuserregisterextend_dongle : [];
				
				// Copy legacy meta data
				$meta_fields = array(
					'telephone' => $legacy_user->telephone,
					'address' => $legacy_user->address,
					'city' => $legacy_user->city,
					'zip' => $legacy_user->zip,
					'country' => $legacy_user->country,
					'company' => $legacy_user->company,
					'user_licenses_enabled' => $user_licenses_enabled,
					'dongle_no' => $dongle_no
				);
				
				if( !empty( $dongle_no ) ){
					$decoded_response = verify_dongle_with_solidcam( $dongle_no, $user_id);
					
					$meta_fields['customer_all_licenses'] = $decoded_response['customer_all_licenses'];
					$meta_fields['customer_contacts'] = $decoded_response['customer_contacts'];
					$meta_fields['customer_modules'] = $decoded_response['customer_modules'];
					$meta_fields['customer_account'] = $decoded_response['customer_account'];
				}
				
				foreach ($meta_fields as $key => $value) {
					if (!empty($value)) {
						update_user_meta($user_id, $key, $value);
					}
				}
			}
		}
	}
	
	return $errors;
}

// Add custom roles on theme activation
function add_custom_user_roles() {
	add_role('customer', 'Customer', array(
		'read' => true,
		// Add other capabilities as needed
	));
	
	add_role('reseller', 'Reseller', array(
		'read' => true,
		// Add other capabilities as needed
	));
	
	add_role('staff', 'Staff', array(
		'read' => true,
		// Add other capabilities as needed
	));
	
	add_role('partner', 'Partner', array(
		'read' => true,
		// Add other capabilities as needed
	));
}
add_action('after_switch_theme', 'add_custom_user_roles');