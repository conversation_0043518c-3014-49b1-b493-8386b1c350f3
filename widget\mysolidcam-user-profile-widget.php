<?php

if (!defined('ABSPATH')) exit; // Exit if accessed directly

class MySolidCam_User_Profile_Widget extends \Elementor\Widget_Base {
    
    public function get_name() {
        return 'mysolidcam_user_profile_widget';
    }

    public function get_title() {
        return __('User Profile', 'solidcam');
    }

    public function get_icon() {
        return 'eicon-lock';
    }

    public function get_categories() {
        return ['general'];
    }

    protected function render() {
        // Get current user information
        $user_id = get_current_user_id();
    
        if (!$user_id) {
            echo '<div>' . esc_html__('You must be logged in to view your profile.', 'solidcam') . '</div>';
            return;
        }
    
        $user_info = get_userdata($user_id);
        $first_name = $user_info->first_name;
        $last_name = $user_info->last_name;
        $email = $user_info->user_email;
    
        echo '<div id="user-profile-widget" class="custom-profile-widget">';
    
        // Display User Information
        echo '<h5 class="profile-header">' . esc_html__('My Profile', 'solidcam') . '</h5>';
        echo '<div class="user-profile-wrp">';
        echo '<div class="user-profile-info">';
        echo '<div class="profile-name"><h5 class="user-name-heading">' . esc_html__('First Name:', 'solidcam') . '</h5> <span>' . esc_html($first_name) . '</span></div>';
        echo '<div class="profile-name"><h5 class="user-name-heading">' . esc_html__('Last Name:', 'solidcam') . '</h5> <span>' . esc_html($last_name) . '</span></div>';

        echo '<div class="profile-email"><h5 class="user-email">'. esc_html__('Email:', 'solidcam'). '</h5> <span>' . esc_html($email) . '</span></div>';
        echo ' </div>';
        echo '<a href="#" class="edit-profile-btn">' . esc_html__('Edit Profile', 'solidcam') . '</a>';

        echo ' </div>';
        
    
        // Edit Profile Button
    
        echo '</div>'; // End of widget
    }

   
}
