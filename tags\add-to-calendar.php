<?php

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class Elementor_Custom_Add_To_Calendar extends \Elementor\Core\DynamicTags\Tag {

    public function get_name() {
        return 'add_to_calendar';
    }

    public function get_title() {
        return __( 'Add To Calendar', 'elementor' );
    }

    public function get_group() {
        return 'site'; // or use other existing groups like 'post', 'archive'
    }

    public function get_categories() {
        return [ \Elementor\Modules\DynamicTags\Module::TEXT_CATEGORY ]; // Define category type
    }

   protected function register_controls() {
        // Show iCal Download
        $this->add_control(
            'show_ical',
            [
                'label' => __( 'Show iCal Download', 'elementor' ),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __( 'Yes', 'elementor' ),
                'label_off' => __( 'No', 'elementor' ),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );
    
        // Show Google Calendar
        $this->add_control(
            'show_google',
            [
                'label' => __( 'Show Google Calendar', 'elementor' ),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __( 'Yes', 'elementor' ),
                'label_off' => __( 'No', 'elementor' ),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );
    
        // Show Outlook Calendar
        $this->add_control(
            'show_outlook',
            [
                'label' => __( 'Show Outlook Calendar', 'elementor' ),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __( 'Yes', 'elementor' ),
                'label_off' => __( 'No', 'elementor' ),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );
    
        // Show Microsoft 365 Calendar
        $this->add_control(
            'show_microsoft',
            [
                'label' => __( 'Show Microsoft 365 Calendar', 'elementor' ),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __( 'Yes', 'elementor' ),
                'label_off' => __( 'No', 'elementor' ),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );
    
        // Show Yahoo Calendar
        $this->add_control(
            'show_yahoo',
            [
                'label' => __( 'Show Yahoo Calendar', 'elementor' ),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __( 'Yes', 'elementor' ),
                'label_off' => __( 'No', 'elementor' ),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );
    
        $this->end_controls_section(); // Properly end the controls section here
    }
    
    public function render() {
        $settings = $this->get_settings_for_display();
        $post_id = get_the_ID();
        
        ob_start();
        render_add_to_calendar($post_id,$settings,'no');
        echo ob_get_clean();

    }

    
}


// Register the Dynamic Tag
add_action('elementor/dynamic_tags/register', function($dynamic_tags) {
    $dynamic_tags->register(new Elementor_Custom_Add_To_Calendar());
});

