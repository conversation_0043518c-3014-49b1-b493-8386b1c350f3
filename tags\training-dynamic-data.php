<?php
	if (!class_exists('Training_Dynamic_Data')) {
		class Training_Dynamic_Data extends \Elementor\Core\DynamicTags\Tag {
			public function get_name() {
				return 'Training-Dynamic-Data';
			}
	
			public function get_title() {
				return 'Training / Form Dynamic Data';
			}
	
			public function get_group() {
				return 'site'; // or use other existing groups like 'post', 'archive'
			}
		
			public function get_categories() {
				return [ \Elementor\Modules\DynamicTags\Module::TEXT_CATEGORY ]; // Define category type
			}
	
			protected function register_controls() {
				$this->add_control(
					'selected_option',
					[
						'label' => 'Select Field',
						'type' => \Elementor\Controls_Manager::SELECT,
						'default' => 'software',
						'options' => [
							'start_date' => 'Start Date',
							'end_date' => 'End Date',
							'software' => 'Software Term',
							'trainingcategory' => 'Training Category',
							'office' => 'Office',
							'user_company' => 'User -> Company',
							'user_phone' => 'User -> Phone',
							'user_street' => 'User -> Street',
							'user_zip' => 'User -> Zip',
							'user_location' => 'User -> Location',
						],
					]
				);
			}
	
			public function render() {
				$selected_option = $this->get_settings('selected_option');
				
				// Get current user ID
				$current_user_id = get_current_user_id();
				
				// Get current post ID
				$post_id = isset( $_REQUEST['training_id'] ) ? $_REQUEST['training_id'] : '';
				
				$output = '';
				
				switch ($selected_option) {
					// Handle taxonomy terms
					case 'software':
						if ($post_id) {
							$terms = get_the_terms($post_id, 'sc_software');
							if (!is_wp_error($terms) && !empty($terms)) {
								// Get just the first term
								$output = $terms[0]->name;
							}
						}
						break;
						
					case 'trainingcategory':
						if ($post_id) {
							$terms = get_the_terms($post_id, 'trainingcategory');
							if (!is_wp_error($terms) && !empty($terms)) {
								// Get just the first term
								$output = $terms[0]->name;
							}
						}
						break;
						
					case 'office':
						if ($post_id) {
							$sc_related_office = get_post_meta($post_id, 'sc_related_office', true);
							if( $sc_related_office ){
								$output = get_the_title( $sc_related_office[0] );
							}
						}
						break;
					
					// Handle user meta fields
					case 'user_company':
						if ($current_user_id) {
							$output = get_user_meta($current_user_id, 'company', true);
						}
						break;
						
					case 'user_phone':
						if ($current_user_id) {
							$output = get_user_meta($current_user_id, 'telephone', true);
						}
						break;
						
					case 'user_zip':
						if ($current_user_id) {
							$output = get_user_meta($current_user_id, 'zip', true);
						}
						break;
						
					case 'user_location':
						if ($current_user_id) {
							$output = get_user_meta($current_user_id, 'address', true);
						}
						break;
						
					case 'start_date':
						if ($post_id) {
							$start_date = get_post_meta($post_id, 'start_date_and_time', true);
							if (!empty($start_date)) {
								// Convert the date string to a timestamp
								$timestamp = strtotime($start_date);
								
								// Format the date in Y.m.d format
								$output = date('d.m.Y', $timestamp);
							}
						}
						break;
						
						
					case 'end_date':
						if ($post_id) {
							$end_date = get_post_meta($post_id, 'end_date_and_time', true);
							if (!empty($end_date)) {
								// Convert the date string to a timestamp
								$timestamp = strtotime($end_date);
								
								// Format the date in Y.m.d format
								$output = date('d.m.Y', $timestamp);
							}
						}
						break;
						
					default:
						$output = '';
						break;
				}
				
				echo wp_kses_post($output);
			}
		}
	}
?>