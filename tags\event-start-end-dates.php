<?php
if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class Elementor_Custom_Event_Dates extends \Elementor\Core\DynamicTags\Tag {
	
	public function get_name() {
		return 'event-start-end-dates';
	}
	
	public function get_title() {
		return __( 'Event Start/End Date', 'elementor' );
	}
	
	public function get_group() {
		return 'site'; // or use other existing groups like 'post', 'archive'
	}
	
	public function get_categories() {
		return [ \Elementor\Modules\DynamicTags\Module::TEXT_CATEGORY ]; // Define category type
	}
	
	protected function register_controls() {
		// Dropdown to select which date to display
		$this->add_control(
			'date_type',
			[
				'label' => __( 'Date Type', 'elementor' ),
				'type' => \Elementor\Controls_Manager::SELECT,
				'default' => 'start_date',
				'options' => [
					'start_date' => __( 'Start Date', 'elementor' ),
					'end_date' => __( 'End Date', 'elementor' ),
				],
				'description' => __( 'Choose which date to display', 'elementor' ),
			]
		);
		
		// ACF field name for start date
		$this->add_control(
			'event_start_date_field',
			[
				'label' => __( 'Event Start Date Field', 'elementor' ),
				'type' => \Elementor\Controls_Manager::TEXT,
				'default' => 'event_start_date',
				'description' => __( 'Enter the ACF field name for the event start date', 'elementor' ),
				'condition' => [
					'date_type' => 'start_date',
				],
			]
		);
		
		// ACF field name for end date
		$this->add_control(
			'event_end_date_field',
			[
				'label' => __( 'Event End Date Field', 'elementor' ),
				'type' => \Elementor\Controls_Manager::TEXT,
				'default' => 'event_end_date',
				'description' => __( 'Enter the ACF field name for the event end date', 'elementor' ),
				'condition' => [
					'date_type' => 'end_date',
				],
			]
		);
	}
	
	public function render() {
		$settings = $this->get_settings_for_display();
		$date_type = $settings['date_type'];
		
		$date_format = 'm/d/Y';
		
		$current_language = apply_filters( 'wpml_current_language', null );
		
		if( $current_language == 'de' ){
			$date_format = 'd.m.Y';
		}
		
		// Get current post ID
		$post_id = get_the_ID();
		
		if ( ! $post_id ) {
			return;
		}
		
		// Determine which field to use based on selected date type
		if ( $date_type === 'start_date' ) {
			$field_name = $settings['event_start_date_field'];
		} else {
			$field_name = $settings['event_end_date_field'];
		}
		
		// Check if ACF is active and get the field value
		if ( function_exists( 'get_field' ) && $field_name ) {
			$date_value = get_field( $field_name, $post_id );
			
			if ( $date_value ) {
				// Handle different ACF date formats
				if ( is_array( $date_value ) ) {
					// If it's an array, look for common date keys
					$date_string = $date_value['date'] ?? $date_value['value'] ?? '';
				} else {
					$date_string = $date_value;
				}
				
				// Convert to timestamp and format
				if ( $date_string ) {
					$timestamp = strtotime( $date_string );
					if ( $timestamp ) {
						echo date( $date_format, $timestamp );
					} else {
						// If strtotime fails, output the raw value
						echo esc_html( $date_string );
					}
				}
			}
		} else {
			// Fallback message if ACF is not available
			echo __( 'ACF not available or field not found', 'elementor' );
		}
	}
	
	/**
	 * Get current language from WPML or WordPress locale
	 */
	public function get_current_language() {
		// Try WPML first
		if ( function_exists( 'wpml_get_current_language' ) ) {
			return wpml_get_current_language();
		}
		
		// Try ICL_LANGUAGE_CODE constant (WPML)
		if ( defined( 'ICL_LANGUAGE_CODE' ) ) {
			return ICL_LANGUAGE_CODE;
		}
		
		// Try Polylang
		if ( function_exists( 'pll_current_language' ) ) {
			return pll_current_language();
		}
		
		// Fallback to WordPress locale
		return get_locale();
	}
}