<?php
/**
 * SolidCAM License Management System
 * 
 * Handles license management, ACF field customization, and Elementor widget modifications.
 * Includes functionality for:
 * - License selection and management
 * - Module selection based on licenses
 * - User role-based content filtering
 */

// Add the custom user meta field in the user profile
 function add_customer_api_response_complete_field($user) {
	 $customer_data = get_user_meta($user->ID, 'customer_api_response_complete', true);
	 ?>
	 <h3>Customer API Response</h3>
	 <table class="form-table">
		 <tr>
			 <th><label for="customer_api_response_complete">API Response</label></th>
			 <td>
				 <textarea name="customer_api_response_complete" id="customer_api_response_complete" rows="10" cols="50" readonly><?php echo json_encode($customer_data); ?></textarea>
				 <p class="description">This is the API response stored for this user.</p>
			 </td>
		 </tr>
	 </table>
	 <?php
 }
 add_action('show_user_profile', 'add_customer_api_response_complete_field');
 add_action('edit_user_profile', 'add_customer_api_response_complete_field');

/**
  * ACF Field Customization for License Selection with Delete Button
  * Populates the license dropdown with user's enabled licenses
  */
 add_filter('acf/load_field/name=choose_license', function ($field) {
	 $field['choices'] = [];
	 $current_user_id = isset($_GET['user_id']) ? $_GET['user_id'] : get_current_user_id();
	 
	 if (!$current_user_id) {
		 $field['choices'][] = 'No License Found';
		 return $field;
	 }
	 
	 $user_licenses = get_user_meta($current_user_id, 'user_licenses_enabled', true);
	 
	 if (!empty($user_licenses) && is_array($user_licenses)) {
		 foreach ($user_licenses as $license_name => $is_enabled) {
			 $field['choices'][$is_enabled] = $is_enabled;
		 }
		 $field['value'] = '';
	 }
	 return $field;
 });
 
 /**
  * Add delete button after the license dropdown field
  */
 add_action('acf/render_field/name=choose_license', function ($field) {
	 echo '<div class="acf-license-delete-wrapper" style="margin-top: 5px;">
		 <button type="button" class="button license-delete-btn" data-field-key="' . esc_attr($field['key']) . '" style="background-color: #dc3545; color: white; border-color: #dc3545; font-size: 12px; padding: 4px 8px;">
			 🗑️ Delete Selected License
		 </button>
	 </div>';
	 ?>
	 <script type="text/javascript">
	 jQuery(document).ready(function($) {
		 // Add delete button after the select field if it doesn't exist
		 var selectField = $('#acf-field_<?php echo $field['key']; ?>');
		 var deleteBtn = selectField.siblings('.license-delete-btn');
		 
		 if (deleteBtn.length === 0) {
			 selectField.after('<button type="button" class="button license-delete-btn" style="margin-left: 10px; background-color: #dc3545; color: white; border-color: #dc3545;">Delete Selected</button>');
		 }
		 
		 // Handle delete button click
		 $(document).on('click', '.license-delete-btn', function(e) {
			 e.preventDefault();
			 
			 var selectElement = $(this).siblings('select');
			 var selectedValue = selectElement.val();
			 var selectedText = selectElement.find('option:selected').text();
			 
			 if (!selectedValue) {
				 alert('Please select a license to delete.');
				 return;
			 }
			 
			 if (confirm('Are you sure you want to delete the license: ' + selectedText + '?')) {
				 // Get current user ID
				 var currentUserId = <?php echo isset($_GET['user_id']) ? $_GET['user_id'] : get_current_user_id(); ?>;
				 
				 alert(currentUserId);
				 
				 // AJAX call to delete the license
				 $.ajax({
					 url: ajaxurl,
					 type: 'POST',
					 data: {
						 action: 'delete_user_license',
						 license_id: selectedValue,
						 user_id: currentUserId,
						 nonce: '<?php echo wp_create_nonce('delete_license_nonce'); ?>'
					 },
					 success: function(response) {
						 if (response.success) {
							 // Remove the option from dropdown
							 selectElement.find('option[value="' + selectedValue + '"]').remove();
							 
							 // Reset selection
							 selectElement.val('').trigger('change');
							 
							 alert('License deleted successfully!');
							 
							 // Reload the page to refresh the field
							 location.reload();
						 } else {
							 alert('Error: ' + response.data.message);
						 }
					 },
					 error: function() {
						 alert('An error occurred while deleting the license.');
					 }
				 });
			 }
		 });
	 });
	 </script>
	 
	 <style>
	 .license-delete-btn:hover {
		 background-color: #c82333 !important;
		 border-color: #bd2130 !important;
	 }
	 </style>
	 <?php
 });
 
 /**
  * AJAX handler to delete user license
  */
 add_action('wp_ajax_delete_user_license', 'handle_delete_user_license');
 function handle_delete_user_license() {
	 // Verify nonce
	 if (!wp_verify_nonce($_POST['nonce'], 'delete_license_nonce')) {
		 wp_die('Security check failed');
	 }
	 
	 $license_id = sanitize_text_field($_POST['license_id']);
	 $user_id = intval($_POST['user_id']);
	 
	 if (!$license_id || !$user_id) {
		 wp_send_json_error(array('message' => 'Invalid license ID or user ID'));
		 return;
	 }
	 
	 // Get current user licenses
	 $user_licenses = get_user_meta($user_id, 'user_licenses_enabled', true);
	 
	 if (!is_array($user_licenses)) {
		 $user_licenses = array();
	 }
	 
	 // Find and remove the license
	 $license_found = false;
	 foreach ($user_licenses as $license_name => $stored_license_id) {
		 if ($stored_license_id === $license_id) {
			 unset($user_licenses[$license_name]);
			 $license_found = true;
			 break;
		 }
	 }
	 
	 if (!$license_found) {
		 wp_send_json_error(array('message' => 'License not found'));
		 return;
	 }
	 
	 // Update user meta
	 update_user_meta($user_id, 'user_licenses_enabled', $user_licenses);
	 
	 // Log the deletion (optional)
	 error_log("License {$license_id} deleted for user {$user_id}");
	 
	 wp_send_json_success(array('message' => 'License deleted successfully'));
 }

/**
 * ACF Field Customization for Module Selection
 * Populates the modules dropdown based on selected license
 */
add_filter('acf/load_field/name=choose_modules', function ($field) {
	$available_modules = [
		'Add-on' => __('SolidCAM Add-in', 'solidcam'),
		'CAD+CAM' => __('SolidCAM CAD/CAM Suite', 'solidcam'),
		'IVCAM' => __('InventorCAM Add-in', 'solidcam'),
		'SCMV' => __('SolidCAM Maker Version', 'solidcam'),
		'SCSE' => __('SolidCAM Add-In for Solid Edge', 'solidcam'),
		'ICMV' => __('InventorCAM Maker Version', 'solidcam')
	];

	$current_user_id = isset($_GET['user_id']) ? $_GET['user_id'] : get_current_user_id();
	if (!$current_user_id) {
		return $field;
	}
	
	$user_licenses = get_user_meta($current_user_id, 'user_licenses_enabled', true);
	if (empty($user_licenses) || $user_licenses = '') {
		$field['choices'][] = 'No License Found';
		return $field;
	}

	// Get user modules override data
	$user_modules_override = get_user_meta($current_user_id, 'user_modules_override', true);
	
	$active_license = get_user_meta($current_user_id, 'active_user_license', true);
	$customer_modules = get_user_meta($current_user_id, 'customer_modules', true);
	
	$choose_modules = [];
	if ( !empty($user_licenses) && is_array($user_licenses) && !empty( $user_modules_override ) ) {
		$choose_modules = $user_modules_override[$user_licenses[0]];
	} else if( $active_license && !empty($customer_modules ) ){
		$choose_modules_data = array_map(function ($sub_array) use ($active_license, $available_modules) {
			if (isset($sub_array['license_number']) && $sub_array['license_number'] == $active_license) {
				$selected_modules = [];
		
				foreach ($available_modules as $module_key => $module) {
					if (isset($sub_array[$module_key]) && $sub_array[$module_key] !== 'false') {
						$selected_modules[] = $module_key;
					}
				}
		
				return $selected_modules;
			}
			return null;
		}, $customer_modules);
		
		// Remove null values
		$choose_modules_data = array_filter($choose_modules_data);
		
		// Flatten the array
		$choose_modules = !empty($choose_modules_data) ? array_merge(...$choose_modules_data) : [];
	}
	
	// Prepare module selection
	$choose_modules_select = [];
	if (!empty($choose_modules) && is_array($choose_modules)) {
		foreach ($choose_modules as $module_value) {
			$choose_modules_select[] = $module_value;
		}
		$field['value'] = $choose_modules_select;
	}

	return $field;
});

/**
 * AJAX Handler for Module Selection by License
 * Returns available modules for a selected license
 */
add_action('wp_ajax_get_modules_by_license', function () {
	$license = sanitize_text_field($_POST['license']);
	$user_id = sanitize_text_field($_POST['user_id']);
	
	if ($user_id) {
		$user_modules_override = get_user_meta($user_id, 'user_modules_override', true);
		$modules = [];
		
		if (!empty($user_modules_override) && is_array($user_modules_override)) {
			$modules = $user_modules_override[$license];
		}
		
		wp_send_json_success(['modules' => $modules]);
	} else {
		wp_send_json_error();
	}
});

/**
 * ACF Save Post Handler
 * Updates user modules override data and handles dongle software key when saving
 */
add_action('acf/save_post', function ($post_id) {
	if (strpos($post_id, 'user_') !== 0) {
		return;
	}
	
	$current_user_id = isset($_POST['user_id']) ? $_POST['user_id'] : 0;
	
	if ($current_user_id) {
		// Existing functionality - Handle user modules override
		$user_modules_override = get_user_meta($current_user_id, 'user_modules_override', true) ?: [];
		$selected_license = get_field('choose_license', $post_id);
		$choose_modules = get_field('choose_modules', $post_id);
		
		$user_modules_override[$selected_license] = $choose_modules;
		update_field('user_modules_override', $user_modules_override, $post_id);
		
		// New functionality - Handle dongle software key
		$dongle_key = get_field('add_donglesoftware-key', $post_id);
		
		if (!empty($dongle_key)) {
			// Sanitize the license key
			$dongle_key = sanitize_text_field($dongle_key);
			
			// Get user's current data
			$customer_modules = get_user_meta($current_user_id, 'customer_modules', true);
			$user_licenses_enabled = get_user_meta($current_user_id, 'user_licenses_enabled', true) ?: [];
			
			// Check if license is already enabled
			if (!in_array($dongle_key, $user_licenses_enabled)) {
				// Validate customer modules exist
				if (is_array($customer_modules)) {
					// Find matching license in customer modules
					$matched_license = current(array_filter($customer_modules, 
						fn($module) => isset($module['license_number']) && $module['license_number'] == $dongle_key
					));
					
					// If valid license found, add it to enabled licenses
					if ($matched_license) {
						$user_licenses_enabled[] = $matched_license['license_number'];
						
						// Update user meta
						update_user_meta($current_user_id, 'user_licenses_enabled', $user_licenses_enabled);
						update_user_meta($current_user_id, 'active_user_license', $matched_license['license_number']);
						
						// Clear the ACF field after successful processing
						update_field('add_donglesoftware-key', '', $post_id);
						
						// Add admin notice for success
						add_action('admin_notices', function() use ($dongle_key) {
							echo '<div class="notice notice-success"><p>' . 
								 sprintf(__('Dongle software key %s has been successfully added to enabled licenses.', 'solidcam'), $dongle_key) . 
								 '</p></div>';
						});
					} else {
						// Add admin notice for invalid key
						add_action('admin_notices', function() use ($dongle_key) {
							echo '<div class="notice notice-error"><p>' . 
								 sprintf(__('Invalid dongle software key: %s', 'solidcam'), $dongle_key) . 
								 '</p></div>';
						});
					}
				}
			} else {
				// License already exists, clear the ACF field
				update_field('add_donglesoftware-key', '', $post_id);
				
				// Add admin notice for duplicate
				add_action('admin_notices', function() use ($dongle_key) {
					echo '<div class="notice notice-warning"><p>' . 
						 sprintf(__('Dongle software key %s is already enabled for this user.', 'solidcam'), $dongle_key) . 
						 '</p></div>';
				});
			}
		}
	}
});

/**
 * License Management AJAX Handlers
 */

/**
 * Remove License Handler
 * Removes a license from user's enabled licenses
 */
add_action('wp_ajax_remove_license', 'remove_license_callback');
function remove_license_callback() {
	if (!check_ajax_referer('mss_ajax_nonce', 'nonce', false)) {
		wp_send_json_error(__('Invalid request. Please try again.', 'solidcam'));
		return;
	}

	$license = sanitize_text_field($_POST['license'] ?? '');
	if (empty($license)) {
		wp_send_json_error(__('No license provided.', 'solidcam'));
		return;
	}

	$current_user = wp_get_current_user();
	if (!$current_user->exists()) {
		wp_send_json_error(__('Please log in to access this functionality.', 'solidcam'));
		return;
	}

	$user_licenses_enabled = get_user_meta($current_user->ID, 'user_licenses_enabled', true);
	if( count( $user_licenses_enabled ) < 2 ){
		wp_send_json_error(__('Failed to remove license.', 'solidcam'));
		return;
	}
	if (!is_array($user_licenses_enabled) || !in_array($license, $user_licenses_enabled)) {
		wp_send_json_error(__('Invalid license.', 'solidcam'));
		return;
	}

	$updated_licenses = array_filter($user_licenses_enabled, fn($enabled_license) => $enabled_license !== $license);
	$update_result = update_user_meta($current_user->ID, 'user_licenses_enabled', $updated_licenses);

	wp_send_json_success($update_result ? 
		__('License removed successfully.', 'solidcam') : 
		__('Failed to remove license.', 'solidcam')
	);
}

/**
 * Add License Handler
 * Adds a new license to user's enabled licenses
 */
add_action('wp_ajax_add_license', 'add_license_callback');
function add_license_callback() {
	if (!check_ajax_referer('mss_ajax_nonce', 'nonce', false)) {
		wp_send_json_error(__('Invalid request. Please try again.', 'solidcam'));
		return;
	}

	$license = sanitize_text_field($_POST['license'] ?? '');
	if (empty($license)) {
		wp_send_json_error(__('No license provided.', 'solidcam'));
		return;
	}

	$current_user = wp_get_current_user();
	if (!$current_user->exists()) {
		wp_send_json_error(__('Please log in to access this functionality.', 'solidcam'));
		return;
	}

	$customer_modules = get_user_meta($current_user->ID, 'customer_modules', true);
	$user_licenses_enabled = get_user_meta($current_user->ID, 'user_licenses_enabled', true) ?: [];

	if (in_array($license, $user_licenses_enabled)) {
		wp_send_json_error(__('License already unlocked.', 'solidcam'));
		return;
	}

	if (!is_array($customer_modules)) {
		wp_send_json_error(__('No customer modules available.', 'solidcam'));
		return;
	}

	$matched_license = current(array_filter($customer_modules, 
		fn($module) => isset($module['license_number']) && $module['license_number'] == $license
	));

	if (!$matched_license) {
		wp_send_json_error(__('Invalid license key.', 'solidcam'));
		return;
	}

	$user_licenses_enabled[] = $matched_license['license_number'];
	$update_result = update_user_meta($current_user->ID, 'user_licenses_enabled', $user_licenses_enabled);
	update_user_meta($current_user->ID, 'active_user_license', $matched_license['license_number']);

	wp_send_json_success($update_result ? 
		__('License registered successfully.', 'solidcam') : 
		__('Failed to register license.', 'solidcam')
	);
}

/**
 * Update Active License Handler
 * Sets a specific license as the active one
 */
add_action('wp_ajax_update_active_license', 'update_active_license_callback');
function update_active_license_callback() {
	if (!check_ajax_referer('mss_ajax_nonce', 'nonce', false)) {
		wp_send_json_error(__('Invalid request. Please try again.', 'solidcam'));
		return;
	}

	$license = sanitize_text_field($_POST['license'] ?? '');
	if (empty($license)) {
		wp_send_json_error(__('No license provided.', 'solidcam'));
		return;
	}

	$current_user = wp_get_current_user();
	if (!$current_user->exists()) {
		wp_send_json_error(__('Please log in to update the license.', 'solidcam'));
		return;
	}

	$user_licenses_enabled = get_user_meta($current_user->ID, 'user_licenses_enabled', true);
	if (!is_array($user_licenses_enabled) || !in_array($license, $user_licenses_enabled)) {
		wp_send_json_error(__('Invalid license.', 'solidcam'));
		return;
	}

	update_user_meta($current_user->ID, 'active_user_license', $license);
	wp_send_json_success(__('Active license updated successfully.', 'solidcam'));
}

/**
 * Elementor Icon List Widget Customization
 */

/**
 * Add Role Filter Controls to Icon List Widget
 */
add_action('elementor/element/icon-list/section_icon/before_section_end', 'enable_icon_list_field_user_roles', 100, 2);
function enable_icon_list_field_user_roles($element, $args) {
	$elementor = \Elementor\Plugin::instance();
	$control_data = $elementor->controls_manager->get_control_from_stack($element->get_name(), 'icon_list');
	
	if (is_wp_error($control_data)) {
		return;
	}
	
	$tmp = new \Elementor\Repeater();
	
	if (!function_exists('get_editable_roles')) {
		require_once ABSPATH . 'wp-admin/includes/user.php';
	}
	
	// Prepare role options
	$roles = get_editable_roles();
	$role_options = ['' => esc_html__('-- Select Role --', 'elementor')];
	foreach ($roles as $role_key => $role_data) {
		$role_options[$role_key] = esc_html__($role_data['name'], 'elementor');
	}
	
	// Add controls
	$tmp->add_control('enable_role_filter', [
		'label' => esc_html__('Enable Role Filter', 'elementor'),
		'type' => \Elementor\Controls_Manager::SWITCHER,
		'default' => 'no',
		'separator' => 'before',
	]);
	
	$tmp->add_control('choose_role', [
		'label' => esc_html__('Choose Role', 'elementor'),
		'type' => \Elementor\Controls_Manager::SELECT2,
		'multiple' => true,
		'options' => $role_options,
		'default' => '',
		'condition' => ['enable_role_filter' => 'yes'],
		'separator' => 'after',
	]);

	// Insert new controls
	$pattern_field = $tmp->get_controls();
	$new_order = [];
	foreach ($control_data['fields'] as $field_key => $field) {
		if ('link' == $field['name']) {
			$new_order[$field_key] = $field;
			$new_order['enable_role_filter'] = $pattern_field['enable_role_filter'];
			$new_order['choose_role'] = $pattern_field['choose_role'];
		}
		$new_order[$field_key] = $field;
	}
	
	$control_data['fields'] = $new_order;
	$element->update_control('icon_list', $control_data);
}

/**
 * Filter Icon List Items Based on User Role
 */
add_action('elementor/frontend/widget/before_render', 'custom_override_icon_list_settings_based_on_roles', 10, 1);
function custom_override_icon_list_settings_based_on_roles($widget) {
	if ('icon-list' !== $widget->get_name()) {
		return;
	}
	
	$settings = $widget->get_settings();
	$css_classes = explode(" ", $settings['_css_classes'] ?? '');
	
	if (!in_array('user-data-wrp', $css_classes)) {
		return;
	}
	
	if (empty($settings['icon_list']) || !is_array($settings['icon_list'])) {
		return;
	}
	
	$current_user = wp_get_current_user();
	
	// Filter items based on user role
	$icon_list = array_filter($settings['icon_list'], function($item) use ($current_user) {
		if (!isset($item['enable_role_filter']) || 'yes' !== $item['enable_role_filter']) {
			return true;
		}
		
		$chosen_role = $item['choose_role'] ?? '';
		if( !empty( $chosen_role ) && !is_array( $chosen_role ) ){
			$chosen_role = explode(",", $chosen_role);
		}
		return empty($chosen_role) || !empty(array_intersect($chosen_role, $current_user->roles));
	});
	
	$widget->set_settings('icon_list', $icon_list);
}