<?php

if (!defined('ABSPATH')) exit; // Exit if accessed directly

class My_Plan_Widget extends \Elementor\Widget_Base {

	public function get_name() {
		return 'my_plan';
	}

	public function get_title() {
		return __('My Plan', 'solidcam');
	}

	public function get_icon() {
		return 'eicon-lock-user';
	}

	public function get_categories() {
		return ['general'];
	}

	protected function render() {
		$current_user = wp_get_current_user();

		// Ensure the user is logged in
		if (!$current_user->exists()) {
			echo '<p>' . esc_html__('Please log in to view your plan.', 'solidcam') . '</p>';
			return;
		}

		$user_id = $current_user->ID;

		// Fetch customer account details from user meta
		$customer_account = get_user_meta($user_id, 'customer_account', true);

		// Validate the customer account meta
		if (empty($customer_account) || !is_array($customer_account)) {
			echo '<p>' . esc_html__('No plan information is available.', 'solidcam') . '</p>';
			return;
		}
		
		$customer_on_sub = isset($customer_account['customer_on_sub']) ? (bool)$customer_account['customer_on_sub'] : false;
		
		// Validate subscription status
		if (!$customer_on_sub) {
			echo '<p>' . esc_html__('No subscription found.', 'solidcam') . '</p>';
			return;
		}

		// Extract subscription details
		
		$sub_end_date = isset($customer_account['sub_end_date']) ? $customer_account['sub_end_date'] : null;

		$sub_end_date = strtotime($sub_end_date);
		$current_date = strtotime(date('d-m-Y'));

		echo '<div class="my-plan-widget">';
		echo '<h5>' . esc_html__('My Plan', 'solidcam') . '</h5>';

		if ($sub_end_date) {
			if ($sub_end_date < $current_date) {
				// Expired subscription
				echo '<h4 class="fs-4 text-danger">';
				echo esc_html__('Expired', 'solidcam');
				echo ' <i class="fal fa-times-circle"></i>';
				echo '</h4>';
			} else {
				// Active subscription
				echo '<h4 class="fs-4 text-success">';
				echo esc_html__('Subscription', 'solidcam');
				echo ' <i class="fal fa-shield-check"></i>';
				echo '</h4>';
			}
		} else {
			// No subscription
			echo '<h4 class="fs-4 text-danger">';
			echo esc_html__('No Subscription', 'solidcam');
			echo ' <i class="fal fa-times-circle"></i>';
			echo '</h4>';
		}

		echo '</div>';
	}
}
