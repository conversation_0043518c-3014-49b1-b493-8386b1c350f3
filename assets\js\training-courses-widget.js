jQuery(document).ready(function($) {
	// Function to update URL parameters
	function updateUrl(type, value, keepOther = true) {
		const url = new URL(window.location.href);
		if (!keepOther) {
			url.searchParams.delete('category');
			url.searchParams.delete('office');
		}
		if (value) {
			url.searchParams.set(type, value);
		} else {
			url.searchParams.delete(type);
		}
		history.pushState({}, '', url);
	}
	// Function to load content with combined filters
	function loadContent(params = {}) {
		$('.training-content').addClass('loading');
		
		var page_url = $('.training-content').attr('data-page-url');
		
		// Get current URL parameters
		const urlParams = new URLSearchParams(window.location.search);
		
		// Get software filter from widget data attributes
		const widgetElement = $('.training-courses-widget');
		let software = [];
		let showAllSoftware = widgetElement.data('show-all-software') === 1;
		
		// Only use software filter if not showing all
		if (!showAllSoftware) {
			software = widgetElement.data('software') || [];
		}
		
		const data = {
			action: 'load_training_content',
			nonce: mss_ajax.training_nonce,
			category: params.category || urlParams.get('category') || '',
			office: params.office || urlParams.get('office') || '',
			software: software,
			show_all_software: showAllSoftware ? '1' : '0',
			page_url : page_url
		};
		
		updateActiveFilters(data.category, data.office);
		
		$.ajax({
			url: mss_ajax.ajax_url,
			type: 'POST',
			data: data,
			success: function(response) {
				if (response.success) {
					$('.training-content').html(response.data);
					
					// Remove any training category blocks that contain no-training-found
					$('.training-category-block').each(function() {
						if ($(this).find('.no-traning-found').length > 0) {
							$(this).remove();
						}
					});
					
					initLoadMore();
				}
			},
			complete: function() {
				$('.training-content').removeClass('loading');
			}
		});
	}
	// Initialize load more functionality
	function initLoadMore() {
		$('.load-more').off('click').on('click', function(e) {
			e.preventDefault();
			const locationBlock = $(this).closest('.location-block');
			locationBlock.find('.hidden-training').slideDown();
			$(this).hide();
		});
	}
	// Update active filter styles for both filters
	function updateActiveFilters(category, office) {
		// Clear all active states
		$('.filter-links a').removeClass('active');
		
		// Set active states based on current filters
		if (category) {
			$(`.category-filter[data-slug="${category}"]`).addClass('active');
		}
		if (office) {
			$(`.office-filter[data-id="${office}"]`).addClass('active');
		}
	}
	// Category filter click handler with toggle
	$(document).on('click', '.category-filter', function(e) {
		e.preventDefault();
		const slug = $(this).data('slug');
		const urlParams = new URLSearchParams(window.location.search);
		const currentCategory = urlParams.get('category');
		
		// Toggle off if clicking the same category
		if (currentCategory === slug) {
			updateUrl('category', '', true);
			loadContent({ 
				category: '',
				office: urlParams.get('office')
			});
		} else {
			// Toggle on new category
			updateUrl('category', slug, true);
			loadContent({ 
				category: slug,
				office: urlParams.get('office')
			});
		}
	});
	// Office filter click handler with toggle
	$(document).on('click', '.office-filter', function(e) {
		e.preventDefault();
		const id = $(this).data('id');
		const urlParams = new URLSearchParams(window.location.search);
		const currentOffice = urlParams.get('office');
		
		// Toggle off if clicking the same office
		if (currentOffice == id) {
			updateUrl('office', '', true);
			loadContent({ 
				category: urlParams.get('category'),
				office: ''
			});
		} else {
			// Toggle on new office
			updateUrl('office', id, true);
			loadContent({ 
				category: urlParams.get('category'),
				office: id
			});
		}
	});
	// Clear filters
	$(document).on('click', '.clear-filters', function(e) {
		e.preventDefault();
		updateUrl('category', '', false); // This will clear both parameters
		loadContent({});
	});
	// Initial load based on URL parameters
	const urlParams = new URLSearchParams(window.location.search);
	loadContent({
		category: urlParams.get('category'),
		office: urlParams.get('office')
	});
});