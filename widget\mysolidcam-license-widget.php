<?php
if (!defined('ABSPATH')) exit;

class Custom_License_Widget extends \Elementor\Widget_Base {
	public function get_name() {
		return 'license_management';
	}

	public function get_title() {
		return __('License Management', 'solidcam');
	}

	public function get_icon() {
		return 'eicon-lock';
	}

	public function get_categories() {
		return ['general'];
	}

	protected function validate_user_access() {
		$user_id = get_current_user_id();
		if (!$user_id) {
			echo '<p>' . esc_html__('You must be logged in to manage licenses.', 'solidcam') . '</p>';
			return false;
		}
		return $user_id;
	}

	protected function get_user_license_data($user_id) {
		return [
			'active_license' => get_user_meta($user_id, 'active_user_license', true),
			'licenses_enabled' => get_user_meta($user_id, 'user_licenses_enabled', true) ?: [],
			'dongle_no' => get_user_meta($user_id, 'dongle_no', true),
			'subscription_status' => get_user_meta($user_id, 'subscription_status', true)
		];
	}

	protected function render_no_licenses_box() {
		?>
		<div class="no-licenses-box">
			<div class="icon-box">
				<i class="fa-solid fa-triangle-exclamation fa-2xl" aria-hidden="true"></i>
			</div>
			<div class="content-box">
				<h4 class="heading"><?php esc_html_e('No dongle number/software key stored', 'solidcam'); ?></h4>
				<p class="description">
					<?php esc_html_e('You can add/update a correct dongle number/software-key to manage your licenses (this is not required)', 'solidcam'); ?>
				</p>
				<a class="add-dongle-button edit-profile-btn" href="#">
					<?php esc_html_e('Add valid dongle number/software-key', 'solidcam'); ?>
					<i class="fa-regular fa-unlock-keyhole ms-2" aria-hidden="true"></i>
				</a>
			</div>
		</div>
		<?php
	}

	protected function render_license_dropdown($licenses_enabled, $active_license) {
		?>
		<select id="license-dropdown">
			<option value=""><?php esc_html_e('Select License', 'solidcam'); ?></option>
			<?php foreach ($licenses_enabled as $license): ?>
				<option value="<?php echo esc_attr($license); ?>" 
						<?php selected($license, $active_license); ?>>
					<?php echo esc_html($license); ?>
				</option>
			<?php endforeach; ?>
		</select>
		<?php
	}

	protected function render_action_buttons() {
		?>
		<button id="remove-license-btn" class="button" data-title="<?php esc_attr_e('Remove the selected Dongle/Software key', 'solidcam'); ?>">
			<i class="fa-light fa-arrows-rotate loader-icon" aria-hidden="true" style="display: none;"></i>
			<i class="fal fa-minus" aria-hidden="true"></i> 
			<span><?php esc_html_e('Remove', 'solidcam'); ?></span>
		</button>
		<button id="toggle-add-license-btn" class="button" data-title="<?php esc_attr_e('Add new Dongle/Software-Key', 'solidcam'); ?>">
			<i class="fa-light fa-arrows-rotate loader-icon" aria-hidden="true" style="display: none;"></i>
			<i class="fal fa-plus" aria-hidden="true"></i> 
			<span><?php esc_html_e('Add', 'solidcam'); ?></span>
		</button>
		<?php
	}

	protected function render_add_license_form() {
		?>
		<div id="add-license-form" class="toggle-form" style="display:none;">
			<label for="add-license"><?php esc_html_e('New Dongle/Software-Key:', 'solidcam'); ?></label>
			<input type="text" id="add-license" style="margin-bottom: 16px;" 
				   pattern="[A-Za-z0-9-]+" title="<?php esc_attr_e('Only alphanumeric characters and hyphens allowed', 'solidcam'); ?>" />
			<div class="checkbox-box">
				<input type="checkbox" id="authorization-checkbox" required />
				<label for="authorization-checkbox">
					<?php esc_html_e('I am authorized to register this SolidCAM Dongle/Software-Key for my company!', 'solidcam'); ?>
				</label>
			</div>
			<button id="send-license-btn" class="button button-primary">
				<?php esc_html_e('Send New Dongle for Registration', 'solidcam'); ?>
			</button>
		</div>
		<?php
	}
	

	protected function render_remove_license_confirm() {
		?>
		<div id="remove-license-confirm" class="toggle-form" style="display:none;">
			<p>
				<?php esc_html_e('Please confirm to delete the key:', 'solidcam'); ?> 
				<span id="selected-license-key"></span>
			</p>
			<button id="confirm-remove-license-btn" class="button button-danger">
				<?php esc_html_e('Remove Dongle/Software-Key', 'solidcam'); ?>
			</button>
		</div>
		<?php
	}

	protected function render() {
		$user_id = $this->validate_user_access();
		if (!$user_id) return;

		$license_data = $this->get_user_license_data($user_id);
		$active_license = $license_data['active_license'];
		$licenses_enabled = $license_data['licenses_enabled'];
		$total_licenses = count($licenses_enabled);

		// Validate active license
		if (!in_array($active_license, $licenses_enabled)) {
			$active_license = null;
		}

		?>
		<div id="license-management-widget" class="custom-license-widget">
			<h5><?php printf(esc_html__('My License (%d)', 'solidcam'), $total_licenses); ?></h5>

			<?php
			if (empty($license_data['dongle_no']) || $license_data['subscription_status'] == 'false') {
				$this->render_no_licenses_box();
			}

			if ($total_licenses > 1) {
				echo '<h2>' . ($active_license ? esc_html($active_license) : esc_html__('No active license', 'solidcam')) . '</h2>';
			}
			?>

			<div class="btn-action-toolbar">
				<?php 
				$this->render_license_dropdown($licenses_enabled, $active_license);
				$this->render_action_buttons();
				?>
			</div>

			<?php
			$this->render_add_license_form();
			$this->render_remove_license_confirm();
			?>
		</div>
	
		<?php
	}
}