<?php

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class Elementor_Custom_Event_Days_Left extends \Elementor\Core\DynamicTags\Tag {

    public function get_name() {
        return 'event-days-left';
    }

    public function get_title() {
        return __( 'Event Days Left', 'elementor' );
    }

    public function get_group() {
        return 'site'; // or use other existing groups like 'post', 'archive'
    }

    public function get_categories() {
        return [ \Elementor\Modules\DynamicTags\Module::TEXT_CATEGORY ]; // Define category type
    }

     protected function register_controls() {
        $this->add_control(
            'event_start_date_field',
            [
                'label' => __( 'Event Start Date Field', 'elementor' ),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => 'event_start_date',
                'description' => __( 'Enter the ACF field name for the event start date', 'elementor' ),
            ]
        );

        $this->add_control(
            'event_end_date_field',
            [
                'label' => __( 'Event End Date Field', 'elementor' ),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => 'event_end_date',
                'description' => __( 'Enter the ACF field name for the event end date', 'elementor' ),
            ]
        );
    }
    
    
    public function render() {
        // Retrieve field names from the user inputs in Elementor
        $start_date_field = $this->get_settings( 'event_start_date_field' );
        $end_date_field = $this->get_settings( 'event_end_date_field' );
    
        // Retrieve ACF start date field value
        $event_start_date = get_post_meta( get_the_ID(), $start_date_field, true );
    
        if ( ! empty( $event_start_date ) ) {
            // Convert start date to timestamp using strtotime
            $start_time = strtotime( $event_start_date );
            $current_time = current_time( 'timestamp' );
            
            if ($start_time === false) {
                echo "<div class='days-left'>Invalid start date format.</div>";
                return;
            }
    
            // Calculate days left
            $days_left = ceil( ( $start_time - $current_time ) / 86400 );
    
            // Output message based on days left
            if ( $days_left > 0 ) {
                echo "<div class='days-left'>Starts in {$days_left} days</div>";
            } elseif ( $days_left == 0 ) {
                echo "<div class='days-left'>The event is today!</div>";
            } else {
                echo "<div class='days-left'>The event has passed</div>";
            }
        }
    }
}

// Register the Dynamic Tag
add_action( 'elementor/dynamic_tags/register', function( $dynamic_tags ) {
    $dynamic_tags->register( new \Elementor_Custom_Event_Days_Left() );
} );


