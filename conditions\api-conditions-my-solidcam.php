<?php

use Elementor\Controls_Manager;
use ElementorPro\Modules\DisplayConditions\Classes\Comparator_Provider;
use ElementorPro\Modules\DisplayConditions\Classes\Comparators_Checker;
use ElementorPro\Modules\DisplayConditions\Conditions\Base\Condition_Base;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

class Api_User_Data_Condition extends Condition_Base {
	const CONDITION_KEY = 'api_user_data';

	public function get_name() {
		return 'api_user_data';
	}

	public function get_label() {
		return esc_html__( 'API User Data', 'elementor-pro' );
	}

	public function get_group() {
		return 'user';
	}

	/**
	 * Check the condition.
	 *
	 * @param array $args The condition arguments.
	 * @return bool
	 */
	public function check( $args ) : bool {
		$current_user_id = get_current_user_id();

		if ( ! $current_user_id ) {
			return false; // No user logged in.
		}

		// Get the user meta JSON.
		$user_meta = get_user_meta( $current_user_id, 'api_object_data', true );

		if ( empty( $user_meta ) ) {
			return false; // No data found.
		}

		// Decode the JSON.
		$api_data = json_decode( $user_meta, true );

		if ( ! is_array( $api_data ) || ! isset( $api_data['modules'] ) ) {
			return false; // Invalid data structure.
		}

		// Check the specific module value.
		$module_name = $args['module_name'];
		$expected_value = $args['expected_value'];

		return isset( $api_data['modules'][ $module_name ] ) && 
			   $api_data['modules'][ $module_name ] === $expected_value;
	}

	public function get_options() {
		$comparators = Comparator_Provider::get_comparators(
			[
				Comparator_Provider::COMPARATOR_IS,
			]
		);

		$this->add_control( 'module_name', [
			'type' => Controls_Manager::TEXT,
			'label' => esc_html__( 'Module Name', 'elementor-pro' ),
			'default' => '',
			'required' => true,
		] );

		$this->add_control( 'expected_value', [
			'type' => Controls_Manager::SELECT,
			'label' => esc_html__( 'Expected Value', 'elementor-pro' ),
			'options' => [
				'true' => esc_html__( 'True', 'elementor-pro' ),
				'false' => esc_html__( 'False', 'elementor-pro' ),
			],
			'default' => 'true',
			'required' => true,
		] );
	}
}
