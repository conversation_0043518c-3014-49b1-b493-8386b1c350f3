<?php

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class Partner_URL_Text extends \Elementor\Core\DynamicTags\Tag {

    public function get_name() {
        return 'partner-url-text';
    }

    public function get_title() {
        return __( 'Partner Button Text', 'elementor' );
    }

    public function get_group() {
        return 'site'; // or use other existing groups like 'post', 'archive'
    }

    public function get_categories() {
        return [ \Elementor\Modules\DynamicTags\Module::TEXT_CATEGORY ]; // Define category type
    }
    
    
    public function render() {
        // Retrieve ACF start date field value
        $url_text = get_post_meta( get_the_ID(), 'url_text', true );
    	
		if( $url_text ){
			echo uc_words( str_replace("-", " ", $url_text) );
		}
    }
}


