(function ($) {
	"use strict";
	function initializeVersioning() {
		$('.datamints-versioning__downloads--latest').each(function() {
			const $container = $(this);
			const data = JSON.parse($container.attr('data-json-dto'));
			const defaultLanguage = $container.attr('data-default-language') || 'en';
			
			let currentVersion = {
				major: data.major_versions[0].version,
				patch: data.major_versions[0].patch_versions[0].version,
				language: defaultLanguage
			};
	
			// Initialize dropdowns
			function initializeVersionsDropdown() {
				const $versionsDropdown = $container.find('.datamints-versioning__versions');
				$versionsDropdown.empty();
				
				data.major_versions.forEach(majorVersion => {
					const $item = $(`<li data-type="item">
						<a class="dropdown-item" href="#" data-version="${majorVersion.version}">
							${majorVersion.version}
						</a>
					</li>`);
					
					$item.find('a').on('click', function(e) {
						e.preventDefault();
						currentVersion.major = majorVersion.version;
						updateDisplay();
					});
					
					$versionsDropdown.append($item);
				});
			}
	
			function initializePatchVersionsDropdown(majorVersion) {
				const $patchesDropdown = $container.find('.datamints-versioning__versions-patches');
				$patchesDropdown.empty();
				
				const patches = data.major_versions.find(mv => mv.version === majorVersion)?.patch_versions || [];
				patches.forEach(patch => {
					const $item = $(`<li data-type="item">
						<a class="dropdown-item" href="#" data-version="${patch.version}">
							${patch.version}
						</a>
					</li>`);
					
					$item.find('a').on('click', function(e) {
						e.preventDefault();
						currentVersion.patch = patch.version;
						updateDisplay();
					});
					
					$patchesDropdown.append($item);
				});
			}
	
			function initializeLanguagesDropdown(patchVersion) {
				const $languagesDropdown = $container.find('.datamints-versioning__languages');
				$languagesDropdown.empty();
				
				const languages = new Set();
				patchVersion.downloads.forEach(download => {
					if (!languages.has(download.language)) {
						languages.add(download.language);
						
						const $item = $(`<li data-type="item">
							<a class="dropdown-item" href="#" data-language="${download.language}">
								${getLanguageName(download.language)}
							</a>
						</li>`);
						
						$item.find('a').on('click', function(e) {
							e.preventDefault();
							currentVersion.language = download.language;
							updateDisplay();
						});
						
						$languagesDropdown.append($item);
					}
				});
			}
	
			function updateDisplay() {
				const majorVersion = data.major_versions.find(mv => mv.version === currentVersion.major);
				const patchVersion = majorVersion?.patch_versions.find(pv => pv.version === currentVersion.patch);
				
				if (!majorVersion || !patchVersion) return;
	
				// Update version display
				$container.find('.datamints-versioning__selected-version-parent').text(majorVersion.version);
				$container.find('.datamints-versioning__selected-version-version').text(patchVersion.version);
				$container.find('.datamints-versioning__selected-version-suffix').text(majorVersion.suffix);
	
				// Update download link
				const download = patchVersion.downloads.find(d => d.language === currentVersion.language);
				if (download) {
					$container.find('.datamints-versioning__link').attr('href', download.url);
					$container.find('.datamints-versioning__selected-language').text(getLanguageName(download.language));
				}
	
				// Update MSI link
				if (patchVersion.msi) {
					$container.find('.datamints-versioning__msi').attr('href', patchVersion.msi).show();
				} else {
					$container.find('.datamints-versioning__msi').hide();
				}
	
				// Update release notes
				if (patchVersion.release_notes) {
					$container.find('.datamints-versioning__selected-release-notes')
						.attr('href', patchVersion.release_notes.url)
						.closest('.datamints-versioning__selected-release-notes-outer').show();
				} else {
					$container.find('.datamints-versioning__selected-release-notes-outer').hide();
				}
	
				// Update release date
				if (patchVersion.released_date) {
					$container.find('.datamints-versioning__selected-release-date').text(patchVersion.released_date);
				}
	
				// Refresh dropdowns
				initializePatchVersionsDropdown(currentVersion.major);
				initializeLanguagesDropdown(patchVersion);
	
				// Update active states
				$container.find('.dropdown-item').removeClass('active');
				$container.find(`[data-version="${currentVersion.major}"]`).addClass('active');
				$container.find(`[data-version="${currentVersion.patch}"]`).addClass('active');
				$container.find(`[data-language="${currentVersion.language}"]`).addClass('active');
			}
	
			function getLanguageName(code) {
				const languages = download_module.languages;
				return languages[code] || code;
			}
	
			// Initialize
			initializeVersionsDropdown();
			updateDisplay();
		});
	}
	jQuery(document).ready(function ($) {
		
		initializeVersioning();
    	
    	//Toggle My Stats
    	$('.info-icon').on('click', function () {
			$('.status-table-view').slideToggle();
		});
    	
    	
		// Toggle Add License Form
		$('#toggle-add-license-btn').on('click', function () {
			$('#add-license-form').slideToggle();
		});
	
		// Toggle Remove License Confirmation
		$('#remove-license-btn').on('click', function () {
			const selectedLicense = $('#license-dropdown').val();
			if (!selectedLicense) {
				alert(mss_ajax.remove_license);
				return;
			}
	
			// Show confirmation box with the selected license key
			$('#selected-license-key').text(selectedLicense);
			$('#remove-license-confirm').slideToggle();
		});
	
        // Confirm Remove License
        $('#confirm-remove-license-btn').on('click', function () {
            const selectedLicense = $('#license-dropdown').val();
        
            if (!selectedLicense) {
                alert(mss_ajax.remove_license);
                return;
            }
        
            var loaderButton = $('#remove-license-btn'); // Target button for loader
            toggleLoader(loaderButton, true); // Show loader before AJAX call
        
            // Send license removal via AJAX
            $.ajax({
                url: mss_ajax.ajax_url,
                method: 'POST',
                data: {
                    action: 'remove_license',
                    license: selectedLicense,
                    nonce: mss_ajax.nonce, // Add nonce here
                },
                beforeSend: function () {
                    console.log('Processing license removal...');
                },
                success: function (response) {
                    if (response.success) {
                        console.log('License removed successfully.');
                        location.reload();
                    } else {
                        alert(response.data || 'An error occurred while removing the license.');
                    }
                },
                error: function () {
                    alert('An unexpected error occurred. Please try again.');
                },
                complete: function () {
                    toggleLoader(loaderButton, false); // Hide loader after AJAX call completes
                }
            });
        });
	
		// Handle License Submission
        $('#send-license-btn').on('click', function () {
            const license = $('#add-license').val().trim();
            const isAuthorized = $('#authorization-checkbox').is(':checked');
        
            if (!license) {
                alert(mss_ajax.software_key);
                return;
            }
        
            if (!isAuthorized) {
                alert(mss_ajax.software_authorize);
                return;
            }
        
            var loaderButton = $('#toggle-add-license-btn'); // Target button for loader
            toggleLoader(loaderButton, true); // Show loader before AJAX call
        
            // Send license registration via AJAX
            $.ajax({
                url: mss_ajax.ajax_url,
                method: 'POST',
                data: {
                    action: 'add_license',
                    license: license,
                    nonce: mss_ajax.nonce, // Add nonce here
                },
                beforeSend: function () {
                    console.log('Processing license registration...');
                },
                success: function (response) {
                    if (response.success) {
                        console.log('New Dongle/Software-Key registered successfully.');
                        location.reload();
                    } else {
                        alert(response.data || 'An error occurred while registering the Dongle/Software-Key.');
                    }
                },
                error: function () {
                    alert('An unexpected error occurred. Please try again.');
                },
                complete: function () {
                    toggleLoader(loaderButton, false); // Hide loader after AJAX call completes
                }
            });
        });
		
		// Loader function
        function toggleLoader(button, show) {
            var loader = $(button).find('.loader-icon'); // The spinning loader
            var plusIcon = $(button).find('.fa-plus'); // Plus icon
            var minusIcon = $(button).find('.fa-minus'); // Minus icon
        
            if (show) {
                loader.show().addClass('fa-spin');
                plusIcon.hide(); // Hide plus icon
                minusIcon.hide(); // Hide minus icon
            } else {
                loader.hide().removeClass('fa-spin');
                plusIcon.show(); // Show plus icon back
                minusIcon.show(); // Show minus icon back
            }
        }
		
		$('#license-dropdown').on('change', function () {
			const selectedLicense = $(this).val();
		
			// Validate that a license is selected
			if (!selectedLicense) {
				alert(mss_ajax.select_license || 'Please select a valid license.');
				return;
			}
		
			// Send the AJAX request to update the active_user_license
			$.ajax({
				url: mss_ajax.ajax_url,
				method: 'POST',
				data: {
					action: 'update_active_license',
					license: selectedLicense,
					nonce: mss_ajax.nonce, // Add the nonce for security
				},
				beforeSend: function () {
					console.log('Updating active license...');
				},
				success: function (response) {
					if (response.success) {
						console.log('Active license updated successfully.');
						location.reload();
					} else {
						alert(response.data || 'An error occurred while updating the license.');
					}
				},
				error: function () {
					alert('An unexpected error occurred. Please try again.');
				}
			});
		});
		
		
		 $("#license-dropdown").select2({
            minimumResultsForSearch: Infinity
        });
        
        
        //Dropdown Toggle Code for My Dashboard
        $('.button-wrp button').on('click', function (e) {
            e.stopPropagation(); // Prevent event bubbling
    
            // Close any other open dropdown menus
            $('.dropdown-menu').not($(this).siblings('.dropdown-menu')).hide();
    
            // Toggle the dropdown for the clicked button
            $(this).siblings('.dropdown-menu').toggle();
        });
    
        // Close all dropdowns if clicking anywhere else on the page
        $(document).on('click', function () {
            $('.dropdown-menu').hide(); // Hide all dropdowns
        });
        
        
       // Open Module Popup
	   $('.add-to-subscription').on('click', function() {
		  var moduleKey = $(this).closest('.card-module').attr('data-key').trim();
	   
		   // Listen for Elementor popup open event
		   $(document).on('elementor/popup/show', function(event, popupID) {
			   if (popupID === 32031) {
				   $('#card-title').val(moduleKey);
			   }
		   });
	   
		   // Show the Elementor popup
		   elementorProFrontend.modules.popup.showPopup({ id: 32031 });
	   });
	   
	   // Handle Send Request button click
	   $(document).on('click', '#send-request', function(e) {
            e.preventDefault(); // Prevent default link behavior
            
            var button = $(this);
            var loader = $('<i class="fa-light fa-arrows-rotate loader-icon fa-spin" aria-hidden="true"></i>'); // Loader icon
        
            // Disable button and add loader
            button.prop('disabled', true);
            button.find('.elementor-button-content-wrapper').prepend(loader);
        
            // Send AJAX request
            $.ajax({
                url: mss_ajax.ajax_url,
                method: 'POST',
                data: {
                    action: 'send_webhook_request',
                    module_key: $('#card-title').val(),
                    security: mss_ajax.addon_nonce // Add nonce for security
                },
                success: function(response) {
                    if (response.success) {
                        // Update popup content
                        $('.popup-heading .elementor-heading-title').text(mss_ajax.module_card_heading);
                        $('.popup-text .modal-body').text(mss_ajax.module_card_text);
                        button.hide();
                    } else {
                        alert('Error: ' + response.data.message);
                    }
                },
                error: function() {
                    alert('An error occurred while processing your request.');
                },
                complete: function() {
                    // Remove loader and re-enable button
                    loader.remove();
                    button.prop('disabled', false);
                }
            });
        });
    
//     $(document).on('click', '#send-request', function(e) {
//     e.preventDefault(); // Prevent default link behavior

//     var button = $(this);
    
//     // Check if loader already exists
//     if (button.find('.loader-icon').length === 0) {
//         var loader = $('<i class="fa-light fa-arrows-rotate loader-icon fa-spin" aria-hidden="true"></i>');
        
//         // Append loader before text
//         button.find('.elementor-button-content-wrapper').prepend(loader);
        
//         // Disable button (optional)
//         button.prop('disabled', true);
//     } else {
//         // Remove loader if clicked again (for testing purposes)
//         button.find('.loader-icon').remove();
//         button.prop('disabled', false);
//     }
// });
        
        
        //Login Button Js
        $(document).on('click', function (e) {
            const $target = $(e.target);
            const dropdown = $('.user-data-wrp');
        
            // Check if the click is outside the .user-info-wrp and .user-data-wrp
            if (!$target.closest('.user-info-wrp').length && !$target.closest('.user-data-wrp').length) {
                dropdown.hide(); // Hide the dropdown
            }
        });
        
        $(document).on('click', '.user-info-wrp', function (e) {
            e.preventDefault();
            const dropdown = $(this).siblings('.user-data-wrp');
            dropdown.toggle(); // Toggle the dropdown
        });
        
        
        // Open Edit Profile Popup with Button
        $('.edit-profile-btn').on('click', function (e) {
			e.preventDefault();
            elementorProFrontend.modules.popup.showPopup({ id: 32152 });
        });
        
	
	});
})(jQuery);