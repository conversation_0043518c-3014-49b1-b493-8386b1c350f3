<?php
if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly
}

class Expiration_Widget extends \Elementor\Widget_Base {
	public function get_name() {
		return 'expiration_widget';
	}

	public function get_title() {
		return __('Expiration Widget', 'solidcam');
	}

	public function get_icon() {
		return 'eicon-calendar';
	}

	public function get_categories() {
		return ['general'];
	}

	protected function render() {
		$current_user = wp_get_current_user();
		
		// Ensure the user is logged in
		if (!$current_user->exists()) {
			echo '<p>' . esc_html__('Please log in to view expiration details.', 'solidcam') . '</p>';
			return;
		}

		// Fetch customer account details from user meta
		$customer_account = get_user_meta($current_user->ID, 'customer_account', true);

		// Validate the customer account meta
		if (empty($customer_account) || !is_array($customer_account)) {
			echo '<p>' . esc_html__('No expiration details available.', 'solidcam') . '</p>';
			return;
		}

		// Extract subscription details
		$sub_end_date = isset($customer_account['sub_end_date']) ? $customer_account['sub_end_date'] : null;
		$customer_on_sub = isset($customer_account['customer_on_sub']) ? (bool)$customer_account['customer_on_sub'] : false;

		// Validate subscription status
		if (!$customer_on_sub) {
			echo '<p>' . esc_html__('No subscription found.', 'solidcam') . '</p>';
			return;
		}
		
		$sub_end_date = strtotime($sub_end_date);
		$sub_end_date = date('d-m-Y', $sub_end_date);

		// try {
			// Convert subscription end date to DateTime for comparison
			$subscription_end_date = DateTime::createFromFormat('d-m-Y', $sub_end_date);
			if (!$subscription_end_date) {
				throw new Exception('Invalid date format');
			}

			$current_date = new DateTime();
			$remaining_days = $current_date->diff($subscription_end_date)->days;
			$is_expired = $subscription_end_date < $current_date;

			// Define progress bar values
			$progress_max = 365; // Assuming a yearly subscription
			$progress_value = max(0, min($remaining_days, $progress_max));
			$progress_percentage = ($progress_value / $progress_max) * 100;

			// Define status and color
			$progress_color = $is_expired ? 'danger' : ($remaining_days <= 30 ? 'warning' : 'success');
			$tooltip_message = $is_expired
				? __('Your subscription has expired.', 'solidcam')
				: sprintf(__('%d days left!', 'solidcam'), $remaining_days);

			// Render widget
			?>
			<div id="expiration-widget" class="tx-datamints-mysolidcam add-on pb-3">
				<h5><?php echo esc_html__('Expiration', 'solidcam'); ?></h5>
				
				<div class="date-box">
					<i class="fal fa-alarm-clock" aria-hidden="true"></i>
					<span><?php echo esc_html($subscription_end_date->format('F d, Y')); ?></span>
				</div>

				<div class="progress position-relative my-3" data-title="<?php echo esc_attr($tooltip_message); ?>">
					<div class="progress-bar bg-<?php echo esc_attr($progress_color); ?>" 
						 role="progressbar" 
						 style="width: <?php echo esc_attr($progress_percentage); ?>%;" 
						 aria-valuenow="<?php echo esc_attr($progress_percentage); ?>" 
						 aria-valuemin="0" 
						 aria-valuemax="100">
						<span class="justify-content-center d-flex position-absolute w-100 text-dark">
							<?php echo esc_html($remaining_days) . ' ' . esc_html__('days', 'solidcam'); ?>
						</span>
					</div>
				</div>
			</div>
			<?php

		// } catch (Exception $e) {
		// 	echo '<p>' . esc_html__('Error processing expiration date.', 'solidcam') . '</p>';
		// 	return;
		// }
	}
}