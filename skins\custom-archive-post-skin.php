<?php
// namespace ElementorPro\Modules\Posts\Skins;

use Elementor\Controls_Manager;
use Elementor\Core\Kits\Documents\Tabs\Global_Colors;
use Elementor\Core\Kits\Documents\Tabs\Global_Typography;
use Elementor\Group_Control_Css_Filter;
use Elementor\Group_Control_Image_Size;
use Elementor\Group_Control_Typography;
use Elementor\Group_Control_Text_Stroke;
use Elementor\Icons_Manager;
use Elementor\Skin_Base as Elementor_Skin_Base;
use Elementor\Widget_Base;
use Elementor\Utils;
use ElementorPro\Modules\Posts\Traits\Button_Widget_Trait;
use ElementorPro\Plugin;
use ElementorPro\Modules\Posts\Widgets\Posts_Base;
use ElementorPro\Core\Utils as ProUtils;


class Skin_Base_Custom_Archive_Post extends \ElementorPro\Modules\ThemeBuilder\Skins\Posts_Archive_Skin_Classic
{

    use Button_Widget_Trait;

    /**
     * @var string Save current permalink to avoid conflict with plugins the filters the permalink during the post render.
     */
    protected $current_permalink;

    public function get_id()
    {
        return 'my-custom-skin-id';
    }

    public function get_title()
    {
        return __('My Custom Skin');
    }

    public function render_thumbnail()
    {
        $read_more = '';
        $thumbnail = $this->get_instance_value('thumbnail');

        if ('none' === $thumbnail && !Plugin::elementor()->editor->is_edit_mode()) {
            return;
        }

        $settings = $this->parent->get_settings();
        $setting_key = $this->get_control_id('thumbnail_size');
        $settings[$setting_key] = [
            'id' => get_post_thumbnail_id(),
        ];
        $thumbnail_html = Group_Control_Image_Size::get_attachment_image_html($settings, $setting_key);

        if (empty($thumbnail_html)) {
            return;
        }

        $optional_attributes_html = $this->get_optional_link_attributes_html();

        ?>
        <a class="elementor-post__thumbnail__link" href="<?php echo esc_attr($this->current_permalink); ?>" <?php echo esc_attr($optional_attributes_html); ?>>
        <div class="elementor-post__thumbnail elementor-fit-height-custom"><?php echo wp_kses_post($thumbnail_html); ?></div>
        <a class="elementor-post__read-more"
           href="<?php echo esc_url($this->current_permalink); ?>" <?php Utils::print_unescaped_internal_string($optional_attributes_html); ?>>
            <?php echo wp_kses_post($read_more); ?>
        </a>

        <?php if ($this->display_read_more_bottom()) : ?>
        </div>
    <?php endif;
    }

    public function render_title()
    {
        if (!$this->get_instance_value('show_title')) {
            return;
        }

        $optional_attributes_html = $this->get_optional_link_attributes_html();

        $tag = $this->get_instance_value('title_tag');
        $cats = get_the_category(get_the_ID());
        if (!empty($cats)) {
            foreach ($cats as $cat): ?>

                <a href="<?php echo get_category_link($cat->cat_ID); ?>">
                    <?php echo $cat->name; ?>
                </a>

            <?php endforeach;
        } ?>
        <<?php Utils::print_validated_html_tag($tag); ?> class="elementor-post__title">
        <a href="<?php echo esc_attr($this->current_permalink); ?>" <?php echo esc_attr($optional_attributes_html); ?>>
            <?php the_title(); ?>
        </a>
        </<?php Utils::print_validated_html_tag($tag); ?>>
        <?php
    }
}