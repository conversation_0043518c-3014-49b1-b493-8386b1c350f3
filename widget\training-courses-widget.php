<?php
if (!defined('ABSPATH')) {
	exit;
}
class Training_Courses_Widget extends \Elementor\Widget_Base {
	public function get_name() {
		return 'training_courses';
	}
	public function get_title() {
		return __('Training Courses', 'solidcam');
	}
	public function get_icon() {
		return 'eicon-posts-grid';
	}
	public function get_categories() {
		return ['general'];
	}
	protected function register_controls() {
		$this->start_controls_section(
			'content_section',
			[
				'label' => __('Content Settings', 'solidcam'),
				'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
			]
		);
		$this->add_control(
			'posts_per_location',
			[
				'label' => __('Initial Posts Per Location', 'solidcam'),
				'type' => \Elementor\Controls_Manager::NUMBER,
				'default' => 2,
			]
		);
		$this->add_control(
			'form_page_url',
			[
				'label' => __('Form Page', 'solidcam'),
				'type' => \Elementor\Controls_Manager::URL
			]
		);
		
		// Get all software categories for the multi-select
		$software_terms = get_terms([
			'taxonomy' => 'sc_software',
			'hide_empty' => false,
		]);
		
		$software_options = ['all' => __('All Software', 'solidcam')];
		foreach ($software_terms as $term) {
			$software_options[$term->term_id] = $term->name;
		}
		
		$this->add_control(
			'selected_software',
			[
				'label' => __('Filter by Software', 'solidcam'),
				'type' => \Elementor\Controls_Manager::SELECT2,
				'options' => $software_options,
				'default' => 'all',
				'multiple' => true,
				'description' => __('Select software categories to filter courses, or leave empty to show all.', 'solidcam'),
			]
		);
		
		$this->end_controls_section();
	}
	protected function render() {
		$settings = $this->get_settings_for_display();
		
		wp_enqueue_script('training-courses-widget');
		wp_enqueue_style('training-courses-widget');
		
		// Get software filter selection
		$selected_software = $settings['selected_software'];
		$form_page_link = $settings['form_page_url'];
		
		// Ensure $selected_software is an array
		if (!is_array($selected_software)) {
			$selected_software = [$selected_software];
		}
		
		// If "all" is selected or no selection is made, show all software
		$show_all_software = in_array('all', $selected_software) || empty($selected_software);
		
		// Convert to array of term IDs for AJAX use (exclude 'all')
		$software_term_ids = array_filter($selected_software, function($value) {
			return $value !== 'all';
		});
		
		// Get training categories
		if ($show_all_software) {
			// If showing all software, get all training categories
			$training_categories = get_terms([
				'taxonomy' => 'trainingcategory',
				'orderby' => 'name',
				'order' => 'ASC'
			]);
		} else {
			// Get only training categories that have trainings with the selected software
			$training_args = [
				'taxonomy' => 'trainingcategory',
				'orderby' => 'name',
				'order' => 'ASC'
			];
			
			// Get all training categories first
			$all_training_categories = get_terms($training_args);
			$filtered_categories = [];
			
			foreach ($all_training_categories as $category) {
				// Find trainings in this category that also have the selected software
				$has_software_trainings = get_posts([
					'post_type' => 'sc_training',
					'posts_per_page' => 1, // We only need to know if any exist
					'tax_query' => [
						'relation' => 'AND',
						[
							'taxonomy' => 'trainingcategory',
							'field' => 'term_id',
							'terms' => $category->term_id
						],
						[
							'taxonomy' => 'sc_software',
							'field' => 'term_id',
							'terms' => $software_term_ids,
							'operator' => 'IN'
						]
					]
				]);
				
				if (!empty($has_software_trainings)) {
					$filtered_categories[] = $category;
				}
			}
			
			$training_categories = $filtered_categories;
		}
		
		// Get office locations
		$offices = get_posts([
			'post_type' => 'sc_office',
			'posts_per_page' => -1,
			'orderby' => 'title',
			'order' => 'ASC'
		]);
		$form_page_url = '';
		if( !empty( $form_page_link['url']) ){
			$form_page_url = $form_page_link['url'];
		}
		?>
		<div class="training-courses-widget" 
			 data-software="<?php echo esc_attr(json_encode($software_term_ids)); ?>"
			 data-show-all-software="<?php echo esc_attr($show_all_software ? '1' : '0'); ?>">
			<div class="filters-section">
				<div class="category-filters">
					<h3><?php _e('Schulungen filtern', 'solidcam'); ?></h3>
					<ul class="filter-links">
						<?php foreach ($training_categories as $category): ?>
							<li>
								<a href="#" 
								   class="category-filter" 
								   data-type="category"
								   data-slug="<?php echo esc_attr($category->slug); ?>">
									<?php echo esc_html($category->name); ?>
								</a>
							</li>
						<?php endforeach; ?>
					</ul>
				</div>
				<div class="office-filters">
					<h3><?php _e('Veranstaltungsort filtern', 'solidcam'); ?></h3>
					<ul class="filter-links">
						<?php foreach ($offices as $office): 
							$sc_location_city = get_post_meta( $office->ID, 'sc_location_city', true );
							?>
							<li>
								<a href="#" 
								   class="office-filter" 
								   data-type="office"
								   data-id="<?php echo esc_attr($office->ID); ?>">
									<?php echo esc_html($sc_location_city); ?>
								</a>
							</li>
						<?php endforeach; ?>
					</ul>
				</div>
				<a href="#" class="clear-filters"><?php _e('Alle Filter löschen', 'solidcam'); ?></a>
			</div>
			<div class="training-content" data-page-url="<?php echo $form_page_url; ?>">
				<!-- Content will be loaded via AJAX -->
			</div>
		</div>
		<?php
	}
}