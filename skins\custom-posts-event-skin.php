<?php
//namespace ElementorPro\Modules\Posts\Skins;

use Elementor\Controls_Manager;
use Elementor\Group_Control_Box_Shadow;
use Elementor\Utils;

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly
}

class Skin_Base_Custom_Posts_Event extends ElementorPro\Modules\Posts\Skins\Skin_Base {

    public function get_id() {
        return 'classic-event-posts';
    }

    public function get_title() {
        return esc_html__( 'Homepage Events', 'elementor-pro' );
    }
    public function render_title() {
        if (!$this->get_instance_value('show_title')) {
            return;
        }
    
        $event_location = get_field('location', get_the_ID());
        $event_start_date = get_field('event_start_date', get_the_ID());
        $event_end_date = get_field('event_end_date', get_the_ID());
        
        // Format dates
        if ($event_start_date && $event_end_date) {
            $start_date = new DateTime($event_start_date);
            $end_date = new DateTime($event_end_date);
        
            // Check if both dates are in the same month and year
            if ($start_date->format('M, Y') === $end_date->format('M, Y')) {
                // If in the same month and year, just show the day range
                $formatted_dates = $start_date->format('j') . '-' . $end_date->format('j') . ' ' . $start_date->format('M, Y');
            } elseif ($start_date->format('Y') === $end_date->format('Y')) {
                // If in different months but the same year
                $formatted_dates = $start_date->format('M j') . ' - ' . $end_date->format('M j, Y');
            } else {
                // If in different years
                $formatted_dates = $start_date->format('M j, Y') . ' - ' . $end_date->format('M j, Y');
            }
        }
    
        $optional_attributes_html = $this->get_optional_link_attributes_html();
        $tag = esc_attr($this->get_instance_value('title_tag')); ?>
    
        <a href="<?php echo esc_url($this->current_permalink); ?>" <?php echo $optional_attributes_html; ?>>
            <<?php echo $tag; ?> class="elementor-post__title">
                <?php if (!empty($formatted_dates)) { ?>
                    <div class="event-end-date">
                        <?php echo esc_html($formatted_dates); ?>
                    </div>
                <?php } ?>
                <div class="title-location-wrp">
                    <?php the_title(); ?>
                    <?php if ($event_location) { ?>
                        <div class="location">
                            <?php 
                                $cleaned_location = wp_strip_all_tags($event_location); // Removes all HTML tags
                                $truncated_text = mb_substr($cleaned_location, 0, 30); // Truncate the cleaned text
                                echo esc_html($truncated_text);
                            ?>
                         
                        </div>
                    <?php } ?>
                </div>
            </<?php echo $tag; ?>>
        </a>
    
    <?php
    }


	
	protected function render_post() {
		$this->render_post_header();
		$this->render_text_header();
		$this->render_title();
		$this->render_text_footer();
		$this->render_post_footer();
	}
}
