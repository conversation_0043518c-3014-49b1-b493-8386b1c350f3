<?php

if ( ! defined( 'ABSPATH' ) ) exit; // Exit if accessed directly

class Elementor_Calendar_Widget extends \Elementor\Widget_Base {
	
	public function get_name() {
		return 'calendar_widget';
	}
	
	public function get_title() {
		return __( 'Calendar Widget', 'plugin-name' );
	}
	
	public function get_icon() {
		return 'eicon-calendar';
	}
	
	public function get_categories() {
		return [ 'general' ];
	}
	
	protected function render() {
		$currentMonth = date('m');
		$currentYear = date('Y');
		?>
		
		<!-- Month Navigation -->
		<div class="row g-0 month_browser align-items-center mb-3">
			<div class="col-auto prev">
				<a href="#" class="prev-month" data-month="<?php echo esc_attr($currentMonth - 1); ?>" data-year="<?php echo esc_attr($currentYear); ?>">
					<i class="fas fa-lg fa-angle-double-left"></i>
				</a>
			</div>
			<div class="col current text-center">
				<span class="lead current-month-year"><?php echo date('F Y'); ?></span>
			</div>
			<div class="col-auto next">
				<a href="#" class="next-month" data-month="<?php echo esc_attr($currentMonth + 1); ?>" data-year="<?php echo esc_attr($currentYear); ?>">
					<i class="fas fa-lg fa-angle-double-right"></i>
				</a>
			</div>
		</div>
		
		<!-- Calendar Table -->
		<div class="table-responsive">
			<table class="align-middle calendar mx-auto">
				<tbody id="calendar-days">
					<!-- Calendar days will be populated here via JavaScript -->
				</tbody>
			</table>
		</div>
		
		<?php
	}
	
	public function __construct($data = [], $args = null) {
		parent::__construct($data, $args);
		// Enqueue scripts and styles
		wp_enqueue_script('calendar-widget-js');
	}
}

// Register the widget
\Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \Elementor_Calendar_Widget());
