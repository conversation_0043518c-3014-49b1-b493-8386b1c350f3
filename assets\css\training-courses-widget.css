/* File: assets/css/training-courses-widget.css */
.training-courses-widget h2,
.training-courses-widget h3,
.training-courses-widget h4,
.training-courses-widget h5,
.training-courses-widget p,
.training-courses-widget ul {
	color: var(--e-global-color-text);
}
.training-courses-widget *:after,
.training-courses-widget *:before {
	font-family: "Font Awesome 6 Sharp";
}
.training-courses-widget .title {
	font-weight: 700;
	font-size: 24px;
	line-height: 29px;
}

/* Filters Section */
.filters-section {
	margin-bottom: 30px;
}

.category-filters,
.office-filters {
	margin-bottom: 20px;
}

.filter-links {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
	list-style: none;
	padding: 0;
	margin: 0;
}

.filter-links a,
.clear-filters {
	display: inline-flex;
	gap: 10px;
	padding: 16px;
	text-decoration: none;
	color: var(--e-global-color-text);
	transition: all 0.3s ease;
	border: 1px solid rgba(0,0,0,0.175);
}


.filter-links a.active {
	background: var(--e-global-color-primary);
	color: var(--e-global-color-dc92533);
}
.filter-links a.active:after,
.clear-filters:after {
	content: "\f058";
	height: 16px;
	width: 16px;
}
.clear-filters:after{
	content: "\f057";
}

/* Content Section */
.training-content {
	position: relative;
}

.training-content.loading::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.7);
	display: flex;
	align-items: center;
	justify-content: center;
}

/* Category Blocks */
.training-category-block {
	display: flex;
	gap: 30px;
	margin-bottom: 48px;
	padding: 20px 0;
	background: #fff;
	border-top: 1px solid #dee2e6;
}

.category-sidebar {
	flex: 0 0 32%;
}

.category-sidebar h3 {
	margin-bottom: 20px;
}

.category-info {
	margin-bottom: 20px;
}

.training-duration {
	border: 1px solid #9eeaf9;
	border-left-width: 4px;
	border-radius: 6px;
	background: #cff4fc;
	padding: 16px;
	display: flex;
	align-items: center;
	gap: 16px;
}
.training-duration:before {
	content: "\f05a";
	font-size: 32px;
	font-weight: 900;
	color: #055160;
}

.training-duration p{
	color: #055160;
	margin: 0
}

/* Location Content */
.location-content {
	flex: 1;
	padding: 16px;
	background: #f8f9fa;
}

.location-content h4 {
	margin-bottom: 20px;
	margin-top: 0;
}

.location-row {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 48px;
	margin-bottom: 48px;
}
.location-content .location-row:last-child {
	margin-bottom: 0;
}

.location-block .top-location-bar {
	display: flex;
	gap: 5px;
	justify-content: space-between;
	align-items: center;
}
.location-block h5 {
	margin: 0;
}
.location-block h5:after {
	content: "\e4d5";
	margin-left: 5px;
	font-weight: 300;
}
.location-block .show-total {
	background: #198754;
	color: white;
	border-radius: 20px;
	padding: 2px 10px;
	min-width: 82px;
	font-size: 15px;
	font-weight: 300;
}
.location-block .load-more {
	display: flex;
	gap: 2px;
	margin-top: 10px;
	color: #198754;
	text-decoration: none;
	cursor: pointer;
}
.location-block .load-more:before {
	content: "\f107";
}

/* Training Items */
.training-item {
	margin-top: 16px;
	background: #fff;
	border-left: 5px solid #dee2e6;
	box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
}

.training-dates {
	padding: 16px;
	display: flex;
	flex-direction: column;
	gap: 8px;
}
.training-dates .date-range {
	font-size: 1rem;
	font-weight: 700;
	color: black;
}
.training-item .reserve-link {
	border-top: 1px solid #dee2e6;
	padding: 8px 16px;
	display: flex;
	gap: 5px;
	color: black;
	padding: 8px 16px;
}
.training-item .reserve-link:after {
	content: "\f101";
	height: 16px;
	width: 16px;
}
.hidden-training {
	display: none;
}


/* Office View */
.office-trainings h2 {
	margin-bottom: 30px;
}

.category-section {
	margin-bottom: 30px;
	padding: 20px;
	background: #fff;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.category-section h3 {
	margin-bottom: 20px;
}

/* Responsive Design */
@media (min-width: 993px) {
	.category-sidebar {
		position: sticky;
		top: 55px; /* Adjust this value based on your header height */
		align-self: flex-start;
		/* max-height: calc(100vh - 40px); /* Viewport height minus top and bottom spacing */
		/* overflow-y: auto; /* In case content is too long */
	}
}
@media (max-width: 1024px) {
	.category-sidebar {
		top: 100px;
	}
}
@media (max-width: 1400px) {
	.location-row {
		grid-template-columns: repeat(2, 1fr);
	}
}
@media (max-width: 1200px) {
	.location-row {
		grid-template-columns: repeat(1, 1fr);
	}
}
@media (max-width: 992px) {
	.training-category-block {
		flex-direction: column;
	}
	
	.category-sidebar {
		flex: none;
	}
}

@media (max-width: 576px) {	
	.filter-links a {
		width: 100%;
		text-align: center;
	}
}