<?php

function import_news_from_typo3() {
	global $wpdb;

	// Your custom SQL query to fetch TYPO3 news data, including path_segment for slug
	$query = "
    SELECT
        news.uid,
        news.pid,
        GROUP_CONCAT(DISTINCT category.title SEPARATOR ', ') AS categories,
        MIN(files.identifier) AS image_path,
        news.deleted,
        news.path_segment,  -- Add the path_segment for the URL slug
        news.title,
        news.bodytext,
        news.sys_language_uid,
        news.datetime
    FROM
        tx_news_domain_model_news AS news
    LEFT JOIN
        sys_file_reference AS file_ref ON file_ref.uid_foreign = news.uid
    LEFT JOIN
        sys_file AS files ON files.uid = file_ref.uid_local
    LEFT JOIN
        sys_category_record_mm AS category_mm ON category_mm.uid_foreign = news.uid
    LEFT JOIN
        sys_category AS category ON category.uid = category_mm.uid_local
    WHERE
        file_ref.tablenames = 'tx_news_domain_model_news'
        AND category_mm.tablenames = 'tx_news_domain_model_news'
        AND news.sys_language_uid = 0
        AND news.deleted = 0
        AND news.pid = 130
    GROUP BY
        news.uid
    ORDER BY
        news.uid ASC;
    ";

	// Run the query to get the TYPO3 news data
	$news_items = $wpdb->get_results($query);

	print_r( count( $news_items ) );
	die;

	foreach ($news_items as $news) {
		// Insert the news item as a custom post type, with path_segment used as the slug
		$post_id = wp_insert_post(array(
			'post_title'   => $news->title,
			'post_content' => $news->bodytext,
			'post_status'  => 'publish',
			'post_date'    => date('Y-m-d H:i:s', $news->datetime),
			'post_type'    => 'success-story',
			'post_name'    => sanitize_title($news->path_segment),  // Use path_segment as the post slug
		));

		if ($post_id) {
			// Handle categories
			$categories = explode(', ', $news->categories);
			$category_ids = array();
			foreach ($categories as $category_name) {

				$category = get_term_by('name', $category_name, 'success-story-category');
				if (!$category) {
					// Create the category if it doesn't exist
					$category = wp_insert_term($category_name, 'success-story-category');
					if (!is_wp_error($category)) {
						$category_ids[] = $category['term_id'];
					}
				} else {
					// If the category exists, add its ID to the array
					$category_ids[] = $category->term_id;
				}
			}
			// Assign the categories to the post
			wp_set_post_terms($post_id, $category_ids, 'success-story-category');

			// Handle the featured image (external image from TYPO3)
			if (!empty($news->image_path)) {
				$image_url = 'https://www.solidcam.com/fileadmin' . $news->image_path;
				$image_id = import_featured_image($post_id, $image_url, $news->title);  // Pass the image title for alt and caption
				if ($image_id) {
					set_post_thumbnail($post_id, $image_id);

					// Move the image to the folder with ID 23 in Real Media Library
					wp_rml_move(20, array($image_id)); // Folder ID is 23
				}
			}
		}
	}
}

// Function to download and attach the image as a featured image
function import_featured_image($post_id, $image_url, $image_title) {
	// Download the image from the provided URL
	$tmp = download_url($image_url);

	if (is_wp_error($tmp)) {
		return false;
	}

	// Prepare file for media library
	$file_array = array(
		'name'     => basename($image_url),
		'tmp_name' => $tmp
	);

	// Check the file type and upload it to the media library
	$id = media_handle_sideload($file_array, $post_id, $image_title);

	// If error storing permanently, unlink the temp file
	if (is_wp_error($id)) {
		@unlink($file_array['tmp_name']);
		return false;
	}

	// Add alt and caption text to the image metadata
	update_post_meta($id, '_wp_attachment_image_alt', sanitize_text_field($image_title));  // Set alt text
	wp_update_post(array(
		'ID'           => $id,
		'post_excerpt' => $image_title,  // Set caption
		'post_title'   => $image_title   // Set image title
	));

	return $id; // Return the attachment ID
}
if( isset($_GET['shah'] ) ){
	// Hook to trigger the import function
	add_action('admin_init', 'import_news_from_typo3');
}


// All the customizations related to the video compression_test()

function get_youtube_video_id_data( $post_id ){
	
	$sc_video_url = get_post_meta($post_id, 'sc_video_url', true);
	if( !$sc_video_url ){
		$sc_video_url = get_post_meta($post_id, 'add_video_link', true);
	}
	
	if( $sc_video_url ){
		$sc_video_url = get_youtube_video_id( $sc_video_url );
	}
	
	return $sc_video_url;
}
add_filter( "get_youtube_video_id_data", "get_youtube_video_id_data", 10, 1 );

function get_video_user_permission_roles( $permission ) {
	
	$current_user = wp_get_current_user();
	$allowed_roles = array( 'administrator', 'reseller', 'partner', 'staff' );
	
	if ( ! empty( $current_user->roles ) && array_intersect( $allowed_roles, $current_user->roles ) ) {
		return true;
	}

	return $permission;
}
add_filter( 'get_video_user_permission', 'get_video_user_permission_roles', 10, 1 );

function user_permission_content_block( $post_id ) {
	
	// Retrieve custom fields (adjust field names as needed)
	$show_video_to_loggedin_user = get_post_meta( $post_id, 'sc_show_video_to_loggedin_user', true );
	$legal_notice_for_signin     = __( 'In order to access all content you must be a Customer on Subscription. Press the button "Sign in or register now", in order to enter your MySolidCAM account or to register a MySolidCAM account.', 'solidcam' );
	$warning_message             = __( 'Unlock with Subscription', 'solidcam' );
	$legal_notice                = __( 'By clicking, you agree to load the YouTube video. More information in the privacy policy', 'solidcam' );

	$user_permission = get_video_user_permission_roles( false );

	if ( $show_video_to_loggedin_user === 'Yes' && ! $user_permission ) {
		$html  = '<span class="legal-notice">';
		$html .= '<i class="far fa-exclamation-circle" aria-hidden="true"></i> ' . esc_html( $legal_notice_for_signin );
		$html .= '<button type="button" class="open-login-popup">';
		$html .= __( 'Sign in or register now', 'solidcam' );
		$html .= '<i class="fa-regular fa-sign-in ms-2" aria-hidden="true"></i>';
		$html .= '</button>';
		$html .= '</span>';
		$html .= '<div class="warning-message">';
		$html .= '<i class="fa-solid fa-circle-exclamation" aria-hidden="true"></i>' . esc_html( $warning_message );
		$html .= '</div>';
	} else {
		// Otherwise, use the alternative legal notice.
		$html  = '<span class="legal-notice">';
		$html .= '<i class="far fa-exclamation-circle" aria-hidden="true"></i> ' . esc_html( $legal_notice );
		$html .= '</span>';
	}
	
	return $html;
}
add_filter( 'user_permission_content_block', 'user_permission_content_block', 10, 1 );



function get_youtube_video_id($url) {
	// Define the regular expression pattern for YouTube video IDs
	$pattern = '/(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/';

	// Run the regex on the provided URL
	if (preg_match($pattern, $url, $matches)) {
		// Return the video ID if found
		return $matches[1];
	}
	
	// Return null if no video ID was found
	return null;
}

function fetch_video_thumbnail_and_set_featured_image($post_id) {
	// Check if it's an autosave, if so, don't proceed
	if (wp_is_post_autosave($post_id) || wp_is_post_revision($post_id)) {
		return;
	}

	// Check the post type
	$post_type = get_post_type($post_id);
	if ($post_type != 'video' && $post_type != 'webinar-recording' && $post_type != 'professor-video' ) {
		return;
	}

	// Fetch the video URL from custom meta fields
	$sc_video_url = get_post_meta($post_id, 'sc_video_url', true);
	if (!$sc_video_url) {
		$sc_video_url = get_post_meta($post_id, 'add_video_link', true);
	}

	if ($sc_video_url) {
		// Extract the YouTube video ID from the URL
		if (preg_match('/(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/', $sc_video_url, $matches)) {
			$video_id = $matches[1];
			$thumbnail_url = 'https://img.youtube.com/vi/' . $video_id . '/maxresdefault.jpg'; // YouTube thumbnail URL

			update_post_meta( $post_id, 'video_image', $thumbnail_url );
		}
	}
}

// Hook into the save post action
add_action('save_post', 'fetch_video_thumbnail_and_set_featured_image');

?>