<?php

class Elementor_Nav_Menu_Widget extends \Elementor\Widget_Base {

	public function get_name() {
		return 'elementor_nav_menu';
	}

	public function get_title() {
		return __( 'Navigation Menu', 'elementor-nav-menu' );
	}

	public function get_icon() {
		// You can pick an icon from here: https://elementor.github.io/elementor-icons/
		return 'eicon-nav-menu';
	}

	public function get_categories() {
		return [ 'general' ]; // Category the widget will appear under in Elementor panel.
	}

	protected function _register_controls() {

		$this->start_controls_section(
			'section_menu',
			[
				'label' => __( 'Menu', 'elementor-nav-menu' ),
			]
		);

		$menus = wp_get_nav_menus();
		$menu_options = [ '0' => __( 'Select Menu', 'elementor-nav-menu' ) ];
		foreach ( $menus as $menu ) {
			$menu_options[ $menu->term_id ] = $menu->name;
		}

		$this->add_control(
			'menu_id',
			[
				'label' => __( 'Select Menu', 'elementor-nav-menu' ),
				'type' => \Elementor\Controls_Manager::SELECT,
				'options' => $menu_options,
			]
		);

		$this->end_controls_section();
	}

	protected function render() {
		$settings = $this->get_settings_for_display();
		if ( ! empty( $settings['menu_id'] ) ) {
			wp_nav_menu( array( 'menu' => $settings['menu_id'] ) );
		}
	}
}
