<?php
function load_training_content() {
	check_ajax_referer('training_courses_nonce', 'nonce');

	$page_url = sanitize_text_field($_POST['page_url']);
	$category_value = sanitize_text_field($_POST['category']);
	$office_value = sanitize_text_field($_POST['office']);
	$software_value = isset($_POST['software']) ? $_POST['software'] : [];
	$show_all_software = isset($_POST['show_all_software']) && $_POST['show_all_software'] == '1';
	$output = '';

	// Sanitize software values if provided
	if (!empty($software_value) && is_array($software_value)) {
		$software_value = array_map('intval', $software_value);
	}

	// Get categories based on filter
	$categories = empty($category_value) ? 
		get_terms([
			'taxonomy' => 'trainingcategory',
			'orderby' => 'name',
			'order' => 'ASC'
		]) : 
		[get_term_by('slug', $category_value, 'trainingcategory')];

	foreach ($categories as $category) {
		if (!$category) continue;

		$output .= '<div class="training-category-block">';
		
		// Left sidebar
		$output .= '<div class="category-sidebar">';
		$output .= '<h3 class="title">' . esc_html($category->name) . '</h3>';
		$output .= '<div class="category-info custom-bullets-style">';
		$output .= wpautop(get_field('sc_training_cat_info', 'trainingcategory_' . $category->term_id));
		$output .= '</div>';
		$output .= '<div class="training-duration">';
		$output .= '<div class="wrapper">' . get_field('sc_training_duration', 'trainingcategory_' . $category->term_id) . '</div>';
		$output .= '</div>';
		$output .= '</div>';

		// Right content - Location blocks
		$output .= '<div class="location-content">';
		$output .= '<h4>' . __('Aktuell geplante Termine in der Niederlassung', 'solidcam') . '</h4>';
		
		// Get offices based on filter
		$offices_query = [
			'post_type' => 'sc_office',
			'posts_per_page' => -1,
			'orderby' => 'title',
			'order' => 'ASC'
		];
		
		if (!empty($office_value)) {
			$offices_query['p'] = intval($office_value);
		}
		
		$office_row = get_posts($offices_query);
		
		$output .= '<div class="location-row">';
		$traning_found = false;
		foreach ($office_row as $office) {
			$today = date('Y-m-d H:i:s', strtotime('today 00:01'));
			
			// Build training query
			$training_query = [
				'post_type' => 'sc_training',
				'posts_per_page' => -1,
				'tax_query' => [
					[
						'taxonomy' => 'trainingcategory',
						'field' => 'term_id',
						'terms' => $category->term_id
					]
				],
				'meta_query' => [
					[
						'key' => 'sc_related_office',
						'value' => '"' . $office->ID . '"',
						'compare' => 'LIKE'
					],
					[
						'key' => 'start_date_and_time',
						'value' => $today,
						'compare' => '>='
					]
				],
				'meta_key' => 'start_date_and_time',
				'orderby' => 'meta_value',
				'order' => 'ASC'
			];
			
			// Add software taxonomy filter if specified
			if (!$show_all_software && !empty($software_value)) {
				$training_query['tax_query'][] = [
					'taxonomy' => 'sc_software',
					'field' => 'term_id',
					'terms' => $software_value,
					'operator' => 'IN'
				];
			}
			
			$trainings = get_posts($training_query);

			if (!empty($trainings)) {
				$traning_found = true;
				$sc_location_city = get_post_meta( $office->ID, 'sc_location_city', true );
				$output .= '<div class="location-block">';
				$output .= '<div class="top-location-bar">';
				$output .= '<h5>' . esc_html($sc_location_city) . '</h5>';
				$output .= '<span class="show-total">' . count($trainings) . ' Termine</span>';
				$output .= '</div>';
				
				foreach ($trainings as $index => $training) {
					$reserve_link = add_query_arg( 'training_id', $training->ID, $page_url );
					$class = $index >= 2 ? 'hidden-training' : '';
					
					$output .= '<div class="training-item ' . $class . '">';
					
					// Get and format dates
					$start_date_raw = get_field('start_date_and_time', $training->ID);
					$end_date_raw = get_field('end_date_and_time', $training->ID);
					
					$start_timestamp = DateTime::createFromFormat('d/m/Y G:i a', $start_date_raw);
					$end_timestamp = DateTime::createFromFormat('d/m/Y G:i a', $end_date_raw);
					
					if ($start_timestamp && $end_timestamp) {
						$german_months = [
							1 => 'Januar', 2 => 'Februar', 3 => 'März', 4 => 'April',
							5 => 'Mai', 6 => 'Juni', 7 => 'Juli', 8 => 'August',
							9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Dezember'
						];
						
						$start_day = $start_timestamp->format('j');
						$end_day = $end_timestamp->format('j');
						$month = $german_months[(int)$start_timestamp->format('n')];
						$year = $start_timestamp->format('Y');
						
						$today = new DateTime();
						$interval = $today->diff($start_timestamp);
						$days_left = $interval->days;
						
						$output .= '<div class="training-dates">';
						$output .= '<div class="date-range">' . $start_day . ( $start_day != $end_day ? '. - ' . $end_day : '' ) . '. ' . $month . ' ' . $year . '</div>';
						if ($days_left > 0) {
							$output .= '<div class="days-left">Beginnt in ' . $days_left . ' Tagen</div>';
						}
						$output .= '</div>';
					}
					
					$output .= '<a href="' . $reserve_link . '" class="reserve-link">Schulungsplatz reservieren</a>';
					$output .= '</div>';
				}

				if (count($trainings) > 2) {
					$output .= '<a href="#" class="load-more">' . __('Weitere Termine anzeigen ...', 'solidcam') . '</a>';
				}
				
				$output .= '</div>';
			}
		}
		
		if( !$traning_found ){
			$output .= '<div class="no-traning-found"></div>';
		}
		
		$output .= '</div>';
		$output .= '</div>';
		$output .= '</div>';
	}

	wp_send_json_success($output);
}
add_action('wp_ajax_load_training_content', 'load_training_content');
add_action('wp_ajax_nopriv_load_training_content', 'load_training_content');